/* =============================================
   🏛️ BAKASANA CORE STYLES - OPTIMIZED
   Wszystkie niezbędne style w jednym pliku
   ============================================= */

/* ===== CSS VARIABLES ===== */
:root {
  /* CORE WARM NEUTRALS - Unified with main.css and tailwind.config.js */
  --sanctuary: #FDFCF8;        /* Główne tło - warm cream */
  --whisper: #F9F7F3;          /* Ultra-subtelne - barely there */
  --rice: #FAF8F4;             /* Rice paper texture */
  --linen: #F6F2E8;            /* Subtelne tła - organic linen */
  --pearl: #F8F5F0;            /* Pearl shimmer */
  --silk: #F4F0E8;             /* Karty - soft silk texture */

  /* WARM CHARCOALS - Unified colors */
  --charcoal: #2A2724;         /* Główny tekst - warm dark */
  --charcoal-light: #4A453F;   /* Lighter text - muted warmth */
  --stone: #8B8680;            /* Subtle text - warm sage */
  --stone-light: #B5B0A8;      /* Light text - warm stone */
  --sage: #A8B5A8;             /* Sage green accent */
  --ash: #D2CDC6;              /* Ash gray */

  /* ENTERPRISE WARM ACCENTS - Unified */
  --temple-gold: #B8935C;      /* Temple gold accent */
  --enterprise-brown: #8B7355; /* Primary accent - sophisticated */
  --terra: #A0845C;            /* Hover states - warm terra */
  --golden-amber: #D4AF37;     /* Golden highlights */

  /* Typography */
  --font-cormorant: 'Cormorant Garamond', serif;
  --font-inter: 'Inter', sans-serif;

  /* Typography scale variables */
  --base-font-size: 16px;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Line heights */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.7;

  /* Letter spacing */
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;

  /* Fluid font sizes */
  --text-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --text-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --text-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --text-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --text-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --text-2xl: clamp(1.5rem, 1.3rem + 1vw, 1.875rem);
  --text-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem);
  --text-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
  --text-5xl: clamp(3rem, 2.5rem + 2.5vw, 4rem);
  --text-6xl: clamp(4rem, 3rem + 5vw, 6rem);
  --text-7xl: clamp(6rem, 4rem + 10vw, 8rem);
}

/* ===== BASE STYLES ===== */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth; /* Enable smooth scroll for better UX */
}

body {
  font-family: var(--font-inter);
  background-color: var(--sanctuary);
  color: var(--charcoal);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== LEGACY HERO STYLES (Kept for compatibility) ===== */
/* Note: New MinimalistHero uses Tailwind classes for better performance */

/* Legacy hero styles removed - using Tailwind classes in new MinimalistHero */

/* Legacy hero styles removed - using modern Tailwind approach */
/* Legacy hero form styles removed - using modern Tailwind approach */

/* Legacy form styles removed - using modern Tailwind approach */

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

/* Legacy responsive hero styles removed - using Tailwind responsive classes */

/* ===== ACCESSIBILITY & MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }

  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== CONTAINER UTILITY ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

@media (min-width: 768px) {
  .container {
    padding: 0 40px;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 60px;
  }
}

/* ===== SKIP LINKS ===== */
.skip-link {
  position: absolute;
  top: -40px;
  left: 8px;
  background: var(--charcoal);
  color: var(--sanctuary);
  padding: 8px 16px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  z-index: 1000;
  transition: top 300ms ease;
}

.skip-link:focus {
  top: 8px;
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}