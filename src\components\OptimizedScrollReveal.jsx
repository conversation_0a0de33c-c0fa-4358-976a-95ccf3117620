'use client';

import { useState, useEffect, useRef } from 'react';

// Lightweight scroll reveal without heavy dependencies
const OptimizedScrollReveal = ({ 
  children, 
  className = '', 
  delay = 0,
  threshold = 0.1,
  animation = 'fadeInUp'
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => setIsVisible(true), delay);
          observer.unobserve(entry.target);
        }
      },
      { threshold, rootMargin: '50px' }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => observer.disconnect();
  }, [delay, threshold]);

  const animationClasses = {
    fadeInUp: isVisible 
      ? 'opacity-100 translate-y-0 scale-100' 
      : 'opacity-0 translate-y-8 scale-95',
    fadeInLeft: isVisible 
      ? 'opacity-100 translate-x-0 scale-100' 
      : 'opacity-0 -translate-x-8 scale-95',
    fadeInRight: isVisible 
      ? 'opacity-100 translate-x-0 scale-100' 
      : 'opacity-0 translate-x-8 scale-95',
    fadeIn: isVisible 
      ? 'opacity-100 scale-100' 
      : 'opacity-0 scale-95'
  };

  return (
    <div
      ref={elementRef}
      className={`
        transition-all duration-700 ease-out
        ${animationClasses[animation]}
        ${className}
      `}
    >
      {children}
    </div>
  );
};

// Staggered reveal for multiple items
export const StaggeredReveal = ({ children, className = '', staggerDelay = 100 }) => {
  return (
    <div className={className}>
      {Array.isArray(children) 
        ? children.map((child, index) => (
            <OptimizedScrollReveal 
              key={index} 
              delay={index * staggerDelay}
              animation="fadeInUp"
            >
              {child}
            </OptimizedScrollReveal>
          ))
        : <OptimizedScrollReveal>{children}</OptimizedScrollReveal>
      }
    </div>
  );
};

export default OptimizedScrollReveal;