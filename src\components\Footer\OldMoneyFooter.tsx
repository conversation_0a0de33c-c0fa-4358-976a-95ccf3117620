'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { useState } from 'react';

interface FooterLink {
  label: string;
  href: string;
}

interface FooterSection {
  title: string;
  links: FooterLink[];
}

const footerSections: FooterSection[] = [
  {
    title: 'Retreaty',
    links: [
      { label: 'Ubud, Bali', href: '/retreaty/bali' },
      { label: 'Sri Lanka Południe', href: '/retreaty/sri-lanka' },
      { label: 'Kalendarz Wydarzeń', href: '/kalendarz' },
      { label: 'Galeria Zdjęć', href: '/galeria' }
    ]
  },
  {
    title: '<PERSON>aj<PERSON><PERSON>',
    links: [
      { label: 'Zajęcia Online', href: '/zajecia-online' },
      { label: 'Indywidualne Sesje', href: '/indywidualne' },
      { label: 'Warsztaty', href: '/warsztaty' },
      { label: '<PERSON><PERSON>y Instruktorskie', href: '/kursy' }
    ]
  },
  {
    title: 'Informacje',
    links: [
      { label: 'O Julii', href: '/o-mnie' },
      { label: 'Blog Podróżniczy', href: '/blog' },
      { label: 'Testimoniale', href: '/testimoniale' },
      { label: 'FAQ', href: '/faq' }
    ]
  }
];

const socialLinks = [
  { name: 'Instagram', href: 'https://instagram.com/fly_with_bakasana', icon: 'instagram' },
  { name: 'Facebook', href: 'https://facebook.com/bakasana.yoga', icon: 'facebook' },
  { name: 'YouTube', href: 'https://youtube.com/@bakasana-yoga', icon: 'youtube' },
  { name: 'LinkedIn', href: 'https://linkedin.com/in/julia-jakubowicz-yoga', icon: 'linkedin' }
];

const OldMoneyFooter: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setIsSubmitting(false);
    setEmail('');
    // Add success notification here
  };

  return (
    <footer className="bg-charcoal text-sanctuary">
      {/* Newsletter Section */}
      <div className="border-b border-ash/20">
        <div className="container mx-auto px-hero-padding py-16">
          <div className="max-w-2xl mx-auto text-center">
            <h3 className="font-cormorant text-heading text-sanctuary mb-4">
              Inspiracje z Podróży
            </h3>
            <p className="text-body text-sage mb-8">
              Otrzymuj najlepsze historie z retreatów, wskazówki dotyczące praktyki 
              i pierwszeństwo w zapisach na nadchodzące wydarzenia.
            </p>
            
            <form onSubmit={handleNewsletterSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Twój adres email"
                required
                className="flex-1 px-4 py-3 bg-ash/10 border border-ash/30 text-sanctuary placeholder-sage focus:outline-none focus:border-enterprise-brown transition-colors duration-300"
              />
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-8 py-3 bg-enterprise-brown text-sanctuary text-small uppercase tracking-[1px] font-medium hover:bg-terra transition-colors duration-300 disabled:opacity-50"
              >
                {isSubmitting ? 'Wysyłanie...' : 'Zapisz się'}
              </button>
            </form>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="container mx-auto px-hero-padding py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <Link href="/" className="inline-block mb-6">
              <div className="font-cormorant text-[28px] font-light text-sanctuary tracking-[0.12em] hover:text-enterprise-brown transition-colors duration-300">
                BAKASANA
              </div>
            </Link>
            <p className="text-body text-sage mb-6 leading-relaxed">
              Transformacyjne retreaty jogi w duchowych sercach Azji. 
              Odkryj ciszę w sercu Bali i Sri Lanki.
            </p>
            
            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center text-caption text-sage">
                <span className="mr-3">📧</span>
                <a href="mailto:<EMAIL>" className="hover:text-enterprise-brown transition-colors duration-300">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center text-caption text-sage">
                <span className="mr-3">📱</span>
                <a href="tel:+48666777888" className="hover:text-enterprise-brown transition-colors duration-300">
                  +48 666 777 888
                </a>
              </div>
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section, index) => (
            <div key={index}>
              <h4 className="font-cormorant text-subtitle text-sanctuary mb-6">
                {section.title}
              </h4>
              <ul className="space-y-3">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      href={link.href}
                      className="text-caption text-sage hover:text-enterprise-brown transition-colors duration-300 hover:translate-x-1 transform inline-block"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Social Media & Bottom Bar */}
        <div className="mt-16 pt-8 border-t border-ash/20">
          <div className="flex flex-col md:flex-row justify-between items-center">
            {/* Social Links */}
            <div className="flex space-x-6 mb-6 md:mb-0">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-10 h-10 border border-ash/30 flex items-center justify-center text-sage hover:text-enterprise-brown hover:border-enterprise-brown transition-all duration-300"
                >
                  {social.icon === 'instagram' && (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                    </svg>
                  )}
                  {social.icon === 'facebook' && (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                  )}
                  {social.icon === 'youtube' && (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                    </svg>
                  )}
                  {social.icon === 'linkedin' && (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                  )}
                </motion.a>
              ))}
            </div>

            {/* Copyright */}
            <div className="text-center md:text-right">
              <p className="text-caption text-sage mb-2">
                © {new Date().getFullYear()} BAKASANA. Wszystkie prawa zastrzeżone.
              </p>
              <div className="flex flex-col md:flex-row md:space-x-6 space-y-2 md:space-y-0">
                <Link href="/privacy" className="text-caption text-sage hover:text-enterprise-brown transition-colors duration-300">
                  Polityka Prywatności
                </Link>
                <Link href="/terms" className="text-caption text-sage hover:text-enterprise-brown transition-colors duration-300">
                  Regulamin
                </Link>
                <Link href="/cookies" className="text-caption text-sage hover:text-enterprise-brown transition-colors duration-300">
                  Pliki Cookie
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default OldMoneyFooter;