'use client';

import React from 'react';
import { cn } from '@/lib/utils';

const Section = ({
  children,
  className,
  spacing = 'default',
  background = 'transparent',
  id,
  ...props
}) => {
  const spacings = {
    none: "py-0",
    sm: "py-12 md:py-16",        // 48px → 64px (8px grid)
    default: "py-20",            // 80px (consistent with brand standard)
    lg: "py-24",                 // 96px (8px grid)
    xl: "py-32",                 // 128px (8px grid)
  };

  const backgrounds = {
    transparent: "",
    primary: "bg-primary",
    secondary: "bg-secondary",
    accent: "bg-accent/5",
    muted: "bg-muted/30",
    gradient: "bg-gradient-elegant",
  };

  return (
    <section 
      id={id}
      className={cn(
        "relative",
        spacings[spacing],
        backgrounds[background],
        className
      )}
      {...props}
    >
      {children}
    </section>
  );
};

const SectionContainer = ({
  children,
  className,
  size = 'default',
  ...props
}) => {
  const sizes = {
    sm: "max-w-4xl",
    default: "max-w-6xl",
    lg: "max-w-7xl",
    xl: "max-w-8xl",
    full: "max-w-full",
  };

  return (
    <div
      className={cn(
        "mx-auto px-4 sm:px-8 lg:px-12", // 16px → 32px → 48px (8px grid)
        sizes[size],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

const SectionHeader = ({
  title,
  subtitle,
  description,
  className,
  alignment = 'center',
  ...props
}) => {
  const alignments = {
    left: "text-left",
    center: "text-center",
    right: "text-right",
  };

  return (
    <div 
      className={cn(
        "mb-16 md:mb-20 lg:mb-24",
        alignments[alignment],
        className
      )}

      {...props}
    >
      {subtitle && (
        <div className="subtitle mb-6 text-xs uppercase tracking-[0.3em] opacity-60 font-light">
          {subtitle}
        </div>
      )}
      
      {title && (
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-serif font-light mb-8 tracking-tight leading-tight">
          {title}
        </h2>
      )}
      
      {description && (
        <div className="max-w-3xl mx-auto">
          <p className="font-light text-xl leading-loose opacity-80">
            {description}
          </p>
        </div>
      )}
    </div>
  );
};

const SectionDivider = ({
  variant = 'line',
  className,
  ...props
}) => {
  const variants = {
    line: (
      <div className="h-px w-24 mx-auto bg-gradient-to-r from-transparent via-accent/30 to-transparent" />
    ),
    dots: (
      <div className="flex items-center justify-center gap-2">
        <div className="w-1 h-1 rectangular bg-accent/40"></div>
        <div className="w-1 h-1 rectangular bg-accent/60"></div>
        <div className="w-1 h-1 rectangular bg-accent/40"></div>
      </div>
    ),
    symbol: (
      <div className="text-2xl text-accent/30 font-serif">❀</div>
    ),
    space: <div className="h-16 md:h-20 lg:h-24" />,
  };

  return (
    <div 
      className={cn(
        "flex items-center justify-center py-12 md:py-16 lg:py-20",
        className
      )}
      {...props}
    >
      {variants[variant]}
    </div>
  );
};

export { Section, SectionContainer, SectionHeader, SectionDivider };
export default Section;
