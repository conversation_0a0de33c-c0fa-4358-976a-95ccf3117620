'use client';

import React, { useState, useEffect } from 'react';

/**
 * 🔧 HYDRATION SAFE WRAPPER
 * 
 * Prevents hydration mismatches by ensuring consistent rendering
 * between server and client. This component delays rendering of
 * its children until after hydration is complete.
 * 
 * Usage:
 * <HydrationSafeWrapper fallback={<div>Loading...</div>}>
 *   <ComponentThatMightCauseHydrationIssues />
 * </HydrationSafeWrapper>
 */

const HydrationSafeWrapper = ({ 
  children, 
  fallback = null,
  className = '',
  ...props 
}) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return fallback;
  }

  return (
    <div className={className} {...props}>
      {children}
    </div>
  );
};

export default HydrationSafeWrapper;
