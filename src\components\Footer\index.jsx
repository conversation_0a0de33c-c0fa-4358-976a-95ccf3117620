'use client';

import React from 'react';
import Link from 'next/link';
import PerformantWhatsApp from '../PerformantWhatsApp';
import { SectionTitle, BodyText } from '@/components/ui/UnifiedTypography';

const Footer = React.memo(function Footer() {
  return (
    <footer className="bg-charcoal text-sanctuary py-20" role="contentinfo" aria-label="Informacje o stronie">
      <div className="container mx-auto px-6 lg:px-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-16">

          {/* Brand Section */}
          <div className="lg:col-span-2">
            <Link href="/" className="inline-block mb-6">
              <h2 className="font-cormorant text-3xl font-light text-sanctuary tracking-wider">
                BAKASANA
              </h2>
            </Link>
            <BodyText className="text-sanctuary/80 mb-6 max-w-md">
              Transformacyjne retreaty jogi na Bali i Sri Lanka z certyfikowaną instruktorką Julią Jakubowicz.
              Odkryj magię duchowej Azji w kameralnych grupach.
            </BodyText>
            <div className="flex space-x-4">
              <a
                href="https://www.instagram.com/fly_with_bakasana"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-enterprise-brown/20 hover:bg-enterprise-brown/30 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-105"
                aria-label="Instagram"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" className="text-sanctuary">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </a>
              <PerformantWhatsApp
                variant="icon"
                className="w-10 h-10 bg-enterprise-brown/20 hover:bg-enterprise-brown/30 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-105"
                message="Cześć! Interesuję się retreatami jogowymi na Bali i Sri Lanka."
              />
              <a
                href="mailto:<EMAIL>"
                className="w-10 h-10 bg-enterprise-brown/20 hover:bg-enterprise-brown/30 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-105"
                aria-label="Email"
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor" className="text-sanctuary">
                  <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                </svg>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-cormorant text-xl font-light text-sanctuary mb-6 tracking-wide">
              Szybkie linki
            </h3>
            <ul className="space-y-3">
              <li>
                <Link href="/retreaty" className="text-sanctuary/70 hover:text-sanctuary transition-colors duration-300">
                  Retreaty
                </Link>
              </li>
              <li>
                <Link href="/program" className="text-sanctuary/70 hover:text-sanctuary transition-colors duration-300">
                  Program
                </Link>
              </li>
              <li>
                <Link href="/o-mnie" className="text-sanctuary/70 hover:text-sanctuary transition-colors duration-300">
                  O mnie
                </Link>
              </li>
              <li>
                <Link href="/zajecia-online" className="text-sanctuary/70 hover:text-sanctuary transition-colors duration-300">
                  Zajęcia online
                </Link>
              </li>
              <li>
                <Link href="/kontakt" className="text-sanctuary/70 hover:text-sanctuary transition-colors duration-300">
                  Kontakt
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="font-cormorant text-xl font-light text-sanctuary mb-6 tracking-wide">
              Kontakt
            </h3>
            <div className="space-y-4">
              <div>
                <p className="text-sanctuary/70 text-sm mb-1">Email</p>
                <a href="mailto:<EMAIL>" className="text-sanctuary hover:text-temple-gold transition-colors duration-300">
                  <EMAIL>
                </a>
              </div>
              <div>
                <p className="text-sanctuary/70 text-sm mb-1">Telefon</p>
                <a href="tel:+48606101523" className="text-sanctuary hover:text-temple-gold transition-colors duration-300">
                  +48 606 101 523
                </a>
              </div>
              <div>
                <p className="text-sanctuary/70 text-sm mb-1">Lokalizacja</p>
                <p className="text-sanctuary">Warszawa, Polska</p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-sanctuary/20 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sanctuary/60 text-sm">
              © 2025 BAKASANA. Wszystkie prawa zastrzeżone.
            </div>
            <div className="flex space-x-6 text-sm">
              <Link href="/polityka-prywatnosci" className="text-sanctuary/60 hover:text-sanctuary transition-colors duration-300">
                Polityka prywatności
              </Link>
              <span className="text-sanctuary/40">•</span>
              <span className="text-sanctuary/60">
                Made with ♡ for spiritual journeys
              </span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
});

export default Footer;