'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { generateFAQSchema } from '@/lib/structuredData';
import OptimizedScrollReveal from './OptimizedScrollReveal';

const defaultFAQs = [
  {
    question: "Czy retreat jest odpowiedni dla początkujących?",
    answer: "Absolutnie! Nasze retreaty są dostosowane do wszystkich poziomów zaawansowania. Ważna jest chęć do nauki i otwartość na nowe doświadczenia, nie poziom umiejętności. Każdą praktykę dostosowujemy indywidualnie do możliwości uczestników."
  },
  {
    question: "Co jest wliczone w cenę retreatu?",
    answer: "W cenie retreatu masz wszystko: 7 nocy w hotelu (pokój dzielony), wszystkie posiłki (śniadanie, lunch, kolacja), transport lotnisko-hotel-lotnisko, praktykę jogi 2x dziennie, medytacje, warsztaty, zwiedzanie najpiękniejszych miejsc Bali oraz opiekę instruktora przez cały pobyt."
  },
  {
    question: "Jak wygląda typowy dzień na retreatie?",
    answer: "Dzień zaczyna się o 6:30 poranną praktyką jogi, następnie śniadanie o 8:00. O 10:00 wyruszamy zwiedzać magiczne miejsca Bali, lunch o 14:00, czas wolny, wieczorna praktyka o 17:00 i kolacja o 19:00. Każdy dzień jest inny i pełen niezapomnianych doświadczeń."
  },
  {
    question: "Czy mogę anulować rezerwację?",
    answer: "Tak, możesz anulować rezerwację do 30 dni przed wyjazdem z zwrotem 50% zadatku. Anulacja do 14 dni przed wyjazdem - zwrot 25% zadatku. Szczegółowe warunki znajdziesz w regulaminie."
  },
  {
    question: "Jakie są warunki płatności?",
    answer: "Przy rezerwacji wpłacasz zadatek w wysokości 30% wartości retreatu. Resztę kwoty wpłacasz 30 dni przed wyjazdem. Akceptujemy przelewy bankowe i BLIK. Wszystkie szczegóły otrzymasz w emailu potwierdzającym rezerwację."
  },
  {
    question: "Co powinienem zabrać ze sobą?",
    answer: "Matę do jogi (jeśli masz ulubioną), wygodne ubrania do praktyki, strój kąpielowy, krem z wysokim filtrem, kapelusz, lekkie ubrania na dzień i coś cieplejszego na wieczór. Pełną listę rzeczy do spakowania otrzymasz po rezerwacji."
  },
  {
    question: "Czy jest możliwość przedłużenia pobytu?",
    answer: "Tak! Możemy pomóc w organizacji dodatkowych dni na Bali przed lub po retreatie. Polecamy hotele, pomagamy z transferami i podpowiadamy, co warto zobaczyć. Skontaktuj się z nami po dokonaniu rezerwacji."
  },
  {
    question: "Jakie są wymagania zdrowotne?",
    answer: "Nie ma specjalnych wymagań zdrowotnych. Ważne jest, żebyś poinformowała nas o ewentualnych kontuzjach, problemach zdrowotnych lub przyjmowanych lekach. Dzięki temu będziemy mogli dostosować praktykę do Twoich potrzeb."
  }
];

export default function FAQSection({ 
  faqs = defaultFAQs, 
  title = "Często zadawane pytania",
  subtitle = "Znajdź odpowiedzi na najczęściej zadawane pytania o nasze retreaty",
  className = ""
}) {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <>
      {/* FAQ Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generateFAQSchema(faqs))
        }}
      />

      <section className={`py-20 ${className}`}>
        <div className="max-w-4xl mx-auto container-padding">
          <OptimizedScrollReveal className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif text-enterprise-brown mb-4">
              {title}
            </h2>
            <p className="text-wood-light text-lg max-w-2xl mx-auto">
              {subtitle}
            </p>
          </OptimizedScrollReveal>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <OptimizedScrollReveal 
                key={index} 
                delay={index * 100}
                className="bg-white rectangular shadow-soft overflow-hidden"
              >
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-shell/10 transition-colors"
                  aria-expanded={openIndex === index}
                  aria-controls={`faq-answer-${index}`}
                >
                  <h3 className="text-lg font-medium text-enterprise-brown pr-4">
                    {faq.question}
                  </h3>
                  <motion.div
                    animate={{ rotate: openIndex === index ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                    className="flex-shrink-0"
                  >
                    <svg 
                      className="w-6 h-6 text-enterprise-brown" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth={2} 
                        d="M19 9l-7 7-7-7" 
                      />
                    </svg>
                  </motion.div>
                </button>
                
                <AnimatePresence>
                  {openIndex === index && (
                    <motion.div
                      id={`faq-answer-${index}`}
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="px-8 pb-6 text-wood-light leading-relaxed">
                        {faq.answer}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </OptimizedScrollReveal>
            ))}
          </div>

          {/* Contact CTA */}
          <OptimizedScrollReveal className="text-center mt-16">
            <div className="bg-gradient-to-r from-temple/5 to-golden/5 rectangular p-8">
              <h3 className="text-xl font-serif text-enterprise-brown mb-4">
                Nie znalazłeś odpowiedzi?
              </h3>
              <p className="text-wood-light mb-6">
                Skontaktuj się z nami bezpośrednio - chętnie odpowiemy na wszystkie pytania!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="mailto:<EMAIL>"
                  className="btn-unified-primary"
                >
                  Napisz email
                </a>
                <a
                  href="tel:+48606101523"
                  className="btn-unified-secondary"
                >
                  Zadzwoń: +48 606 101 523
                </a>
              </div>
            </div>
          </OptimizedScrollReveal>
        </div>
      </section>
    </>
  );
}

// Compact FAQ for sidebars or smaller sections
export function CompactFAQ({ faqs, maxItems = 5 }) {
  const [openIndex, setOpenIndex] = useState(null);
  const displayFAQs = faqs.slice(0, maxItems);

  return (
    <div className="space-y-3">
      {displayFAQs.map((faq, index) => (
        <div key={index} className="border border-enterprise-brown/10 rectangular overflow-hidden">
          <button
            onClick={() => setOpenIndex(openIndex === index ? null : index)}
            className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-enterprise-brown/5 transition-colors"
          >
            <span className="text-sm font-medium text-enterprise-brown">
              {faq.question}
            </span>
            <motion.svg
              animate={{ rotate: openIndex === index ? 180 : 0 }}
              className="w-4 h-4 text-enterprise-brown flex-shrink-0 ml-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </motion.svg>
          </button>
          
          <AnimatePresence>
            {openIndex === index && (
              <motion.div
                initial={{ height: 0 }}
                animate={{ height: 'auto' }}
                exit={{ height: 0 }}
                className="overflow-hidden"
              >
                <div className="px-4 pb-3 text-sm text-wood-light">
                  {faq.answer}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      ))}
    </div>
  );
}

// FAQ data for different pages
export const retreatFAQs = [
  {
    question: "Ile osób uczestniczy w retreatie?",
    answer: "Nasze grupy są małe - maksymalnie 12 osób. Dzięki temu każdy uczestnik otrzymuje indywidualną uwagę i możemy stworzyć intymną, rodzinną atmosferę."
  },
  {
    question: "Czy są jakieś ograniczenia wiekowe?",
    answer: "Retreaty są przeznaczone dla osób dorosłych (18+). Nie ma górnej granicy wieku - ważna jest dobra kondycja zdrowotna i chęć do aktywnego uczestnictwa."
  },
  {
    question: "Jak wygląda kwestia bezpieczeństwa na Bali?",
    answer: "Bali to bardzo bezpieczna wyspa. Jesteśmy tam regularnie i znamy lokalne zwyczaje. Zapewniamy 24/7 wsparcie i jesteśmy zawsze w kontakcie z grupą."
  }
];

export const bookingFAQs = [
  {
    question: "Jak długo mam na wpłacenie reszty kwoty?",
    answer: "Resztę kwoty (70% wartości retreatu) wpłacasz 30 dni przed wyjazdem. Otrzymasz przypomnienie emailem z dokładnymi instrukcjami."
  },
  {
    question: "Czy mogę zmienić termin rezerwacji?",
    answer: "Tak, możesz zmienić termin do 60 dni przed wyjazdem bez dodatkowych opłat (pod warunkiem dostępności miejsc w nowym terminie)."
  },
  {
    question: "Co w przypadku problemów z lotem?",
    answer: "Pomagamy w organizacji lotów i jesteśmy w kontakcie przed wyjazdem. W przypadku opóźnień lub odwołań lotów, dostosowujemy transfer i program."
  }
];
