import Link from 'next/link';
import OptimizedIcon from './OptimizedIcon';

const InternalLinks = ({ currentPage, className = "" }) => {
  // Mapa internal links dla różnych stron
  const linkSuggestions = {
    'home': [
      { href: '/program', label: 'Program Retreatów', icon: 'Calendar' },
      { href: '/blog', label: 'Blog o Jodze', icon: 'BookOpen' },
      { href: '/kontakt', label: 'Skontaktuj się', icon: 'Mail' }
    ],
    'blog': [
      { href: '/program', label: 'Zobacz Program Retreatu', icon: 'Calendar' },

      { href: '/zajecia-online', label: 'Zajęcia Online', icon: 'Video' }
    ],
    'program': [
      { href: '/blog', label: 'Przeczytaj Blog', icon: 'BookOpen' },
      { href: '/galeria', label: '<PERSON><PERSON><PERSON><PERSON>', icon: 'Camera' },
      { href: '/kontakt', label: 'Skontaktuj się', icon: 'Mail' }
    ],

    'zajecia-online': [
      { href: '/program', label: 'Retreaty na Bali', icon: 'MapPin' },
      { href: '/blog', label: 'Blog o Jodze', icon: 'BookOpen' },
      { href: '/kontakt', label: 'Zapisz się', icon: 'Mail' }
    ],
    'galeria': [
      { href: '/program', label: 'Program Wyjazdów', icon: 'Calendar' },
      { href: '/blog', label: 'Relacje z Podróży', icon: 'BookOpen' },

    ],
    'kontakt': [
      { href: '/program', label: 'Zobacz Program', icon: 'Calendar' },
      { href: '/zajecia-online', label: 'Zajęcia Online', icon: 'Video' },
      { href: '/blog', label: 'Przeczytaj Blog', icon: 'BookOpen' }
    ]
  };

  const links = linkSuggestions[currentPage] || [];

  if (links.length === 0) return null;

  return (
    <section className={`py-12 ${className}`}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-gradient-to-r from-temple/5 to-bamboo/5 rectangular p-8 border border-enterprise-brown/10">
          <h3 className="text-xl font-serif text-enterprise-brown mb-6 text-center">
            Może Cię również zainteresować
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {links.map((link, index) => (
              <Link
                key={index}
                href={link.href}
                className="group flex items-center gap-3 p-4 bg-shell/60 backdrop-blur-sm rectangular border border-enterprise-brown/10 hover:border-enterprise-brown/20 hover:shadow-medium transition-all duration-300 hover:-translate-y-1"
              >
                <div className="p-2 bg-enterprise-brown/10 rectangular group-hover:bg-enterprise-brown/20 transition-colors duration-300">
                  <OptimizedIcon 
                    name={link.icon} 
                    className="w-4 h-4 text-enterprise-brown" 
                  />
                </div>
                <span className="text-enterprise-brown font-medium text-sm group-hover:text-golden transition-colors duration-300">
                  {link.label}
                </span>
                <OptimizedIcon 
                  name="ArrowRight" 
                  className="w-4 h-4 text-enterprise-brown/40 ml-auto group-hover:text-enterprise-brown group-hover:translate-x-1 transition-all duration-300" 
                />
              </Link>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

// Komponent dla related blog posts z enhanced linking
export const RelatedBlogPosts = ({ currentPost, allPosts }) => {
  // Znajdź powiązane posty na podstawie tagów
  const relatedPosts = allPosts
    .filter(post => 
      post.slug !== currentPost.slug && 
      post.tags?.some(tag => currentPost.tags?.includes(tag))
    )
    .slice(0, 3);

  if (relatedPosts.length === 0) return null;

  return (
    <section className="py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h3 className="text-2xl font-serif text-enterprise-brown mb-2">Powiązane Artykuły</h3>
          <p className="text-wood-light/80 text-sm">Kontynuuj czytanie na podobne tematy</p>
          <div className="w-12 h-0.5 bg-enterprise-brown/20 mx-auto mt-4"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {relatedPosts.map(post => (
            <Link
              href={`/blog/${post.slug}`}
              key={post.slug}
              className="group bg-shell/60 backdrop-blur-sm rectangular border border-enterprise-brown/10 overflow-hidden hover:shadow-medium transition-all duration-300 hover:-translate-y-1"
            >
              {post.imageUrl && (
                <div className="relative h-40 overflow-hidden">
                  <img
                    src={post.imageUrl}
                    alt={post.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-temple/20 to-transparent"></div>
                </div>
              )}
              <div className="p-4">
                <h4 className="font-serif text-enterprise-brown text-lg leading-tight group-hover:text-golden transition-colors duration-300 mb-2">
                  {post.title}
                </h4>
                <p className="text-wood-light/70 text-sm line-clamp-2 mb-3">
                  {post.excerpt}
                </p>
                <div className="flex items-center gap-2 text-xs text-enterprise-brown/60">
                  <OptimizedIcon name="Calendar" className="w-3 h-3" />
                  <span>{new Date(post.date).toLocaleDateString('pl-PL')}</span>
                  <span>•</span>
                  <span>{post.category}</span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default InternalLinks;
