'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { mainNavItems } from '@/data/navigationLinks';

export default function ClientNavbar() {
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  
  useEffect(() => {
    const handleScroll = () => setScrolled(window.scrollY > 20);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [pathname]);

  const isActiveLink = (href) => {
    if (href === '/') return pathname === '/';
    return pathname.startsWith(href);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <>
      <nav className={`
        fixed top-0 left-0 right-0 z-50
        transition-all duration-500 ease-out
        ${scrolled 
          ? 'bg-white/90 backdrop-blur-md py-5 shadow-[0_1px_20px_rgba(0,0,0,0.03)] border-b border-enterprise-brown/10' 
          : 'bg-transparent py-8'
        }
      `}>
        <div className="container mx-auto">
          <div className="flex justify-between items-center">
            
            {/* Logo */}
            <Link 
              href="/" 
              className={`
                font-cormorant text-[22px] font-light tracking-[0.12em]
                transition-all duration-500 group
                ${scrolled ? 'text-charcoal' : 'text-charcoal/90'}
                hover:text-enterprise-brown
              `}
            >
              BAKASANA
            </Link>
            
            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center gap-12">
              {mainNavItems.slice(1).map((item) => {
                // For dropdown items, show main link only
                const href = item.dropdown ? '/program' : item.href;
                
                const isActive = isActiveLink(href);
                
                return (
                  <Link
                    key={item.label}
                    href={href}
                    className={`
                      relative text-[11px] font-light tracking-[0.08em] uppercase group
                      transition-all duration-300
                      ${scrolled ? 'text-charcoal' : 'text-charcoal/80'}
                      hover:text-enterprise-brown hover:-translate-y-[1px]
                      ${isActive ? 'text-enterprise-brown' : ''}
                      ${item.highlight ? 'font-normal text-enterprise-brown' : ''}
                    `}
                  >
                    {item.label}
                    
                    {/* Elegant underline for active/hover */}
                    <span className={`
                      absolute -bottom-1 left-0 right-0 h-[1px]
                      bg-enterprise-brown transition-all duration-300
                      ${isActive ? 'opacity-100' : 'opacity-0'}
                      group-hover:opacity-100
                    `} />
                  </Link>
                );
              })}
            </nav>
            
            {/* Mobile menu button */}
            <button 
              className="lg:hidden"
              onClick={toggleMobileMenu}
              aria-label="Toggle mobile menu"
            >
              <div className="space-y-1.5">
                <span className={`
                  block w-6 h-[1.5px] transition-all duration-300
                  ${scrolled ? 'bg-charcoal' : 'bg-charcoal/80'}
                  ${mobileMenuOpen ? 'rotate-45 translate-y-2' : ''}
                `} />
                <span className={`
                  block w-6 h-[1.5px] transition-all duration-300
                  ${scrolled ? 'bg-charcoal' : 'bg-charcoal/80'}
                  ${mobileMenuOpen ? 'opacity-0' : ''}
                `} />
                <span className={`
                  block w-6 h-[1.5px] transition-all duration-300
                  ${scrolled ? 'bg-charcoal' : 'bg-charcoal/80'}
                  ${mobileMenuOpen ? '-rotate-45 -translate-y-2' : ''}
                `} />
              </div>
            </button>
            
          </div>
        </div>
      </nav>

      {/* Mobile Menu */}
      <div className={`
        fixed inset-0 z-40 lg:hidden
        transition-all duration-300 ease-out
        ${mobileMenuOpen ? 'opacity-100 visible' : 'opacity-0 invisible'}
      `}>
        {/* Backdrop */}
        <div 
          className="absolute inset-0 bg-charcoal/20 backdrop-blur-sm"
          onClick={() => setMobileMenuOpen(false)}
        />
        
        {/* Menu Panel */}
        <div className={`
          absolute top-0 right-0 h-full w-80 max-w-[85vw]
          bg-white/95 backdrop-blur-md shadow-2xl
          transform transition-transform duration-300 ease-out
          ${mobileMenuOpen ? 'translate-x-0' : 'translate-x-full'}
        `}>
          <div className="pt-24 px-8">
            <nav className="space-y-6">
              {mainNavItems.map((item) => {
                // For dropdown items, show main link only
                const href = item.dropdown ? '/program' : item.href;
                
                const isActive = isActiveLink(href);
                
                return (
                  <Link
                    key={item.label}
                    href={href}
                    className={`
                      block text-lg font-light tracking-[0.02em]
                      transition-all duration-300
                      ${isActive ? 'text-enterprise-brown' : 'text-charcoal'}
                      hover:text-enterprise-brown hover:translate-x-2
                      ${item.highlight ? 'font-normal' : ''}
                    `}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {item.label}
                  </Link>
                );
              })}
            </nav>
          </div>
        </div>
      </div>
    </>
  );
}