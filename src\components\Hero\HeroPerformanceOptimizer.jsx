'use client';

import { useEffect } from 'react';

/**
 * HeroPerformanceOptimizer - Optimizes hero section performance
 * - Preloads critical hero images
 * - Injects critical CSS for above-the-fold content
 * - Optimizes Core Web Vitals
 */
const HeroPerformanceOptimizer = () => {
  useEffect(() => {
    // Preload critical hero images
    const preloadImages = () => {
      const imageUrls = [
        '/images/background/bali-hero.webp',
        '/images/background/bali-hero-1200.avif',
        '/images/background/bali-hero-low-res.webp'
      ];

      imageUrls.forEach(url => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = url;
        link.type = url.includes('.avif') ? 'image/avif' : 'image/webp';
        document.head.appendChild(link);
      });
    };

    // Inject critical CSS for hero section
    const injectCriticalCSS = () => {
      const criticalCSS = `
        /* Critical Hero CSS for above-the-fold content */
        .hero-critical {
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #FDFCF8 0%, #F9F7F3 100%);
          will-change: transform;
          contain: layout style paint;
        }
        
        .hero-bg-critical {
          position: absolute;
          inset: 0;
          background-color: #FDFCF8;
          background-image: url('/images/background/bali-hero-low-res.webp');
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          will-change: transform;
        }
        
        .hero-content-critical {
          position: relative;
          z-index: 20;
          text-align: center;
          max-width: 96rem;
          margin: 0 auto;
          padding: 0 2rem;
        }
        
        .hero-title-critical {
          font-family: 'Cormorant Garamond', serif;
          font-weight: 300;
          color: #2A2724;
          line-height: 0.95;
          margin-bottom: 1rem;
          font-size: clamp(4rem, 8vw, 8rem);
          letter-spacing: 0.2em;
          will-change: transform, opacity;
        }
        
        /* Optimize backdrop-blur for better performance */
        .backdrop-blur-optimized {
          backdrop-filter: blur(4px);
          -webkit-backdrop-filter: blur(4px);
          will-change: backdrop-filter;
        }
        
        /* Reduce motion for better performance */
        @media (prefers-reduced-motion: reduce) {
          .hero-critical * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }
        }
      `;

      const style = document.createElement('style');
      style.textContent = criticalCSS;
      style.id = 'hero-critical-css';
      document.head.appendChild(style);
    };

    // Optimize fonts loading
    const optimizeFonts = () => {
      // Preload critical fonts
      const fontUrls = [
        'https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;1,300;1,400&display=swap',
        'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap'
      ];

      fontUrls.forEach(url => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = url;
        link.onload = function() { this.rel = 'stylesheet'; };
        document.head.appendChild(link);
      });
    };

    // Performance optimizations
    const optimizePerformance = () => {
      // Enable GPU acceleration for smooth animations
      document.documentElement.style.setProperty('--gpu-acceleration', 'translateZ(0)');
      
      // Optimize scroll performance
      if ('scrollBehavior' in document.documentElement.style) {
        document.documentElement.style.scrollBehavior = 'smooth';
      }
      
      // Prefetch critical resources
      const prefetchUrls = ['/harmonogram', '/kontakt'];
      prefetchUrls.forEach(url => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = url;
        document.head.appendChild(link);
      });
    };

    // Run optimizations
    preloadImages();
    injectCriticalCSS();
    optimizeFonts();
    optimizePerformance();

    // Cleanup function
    return () => {
      const criticalStyle = document.getElementById('hero-critical-css');
      if (criticalStyle) {
        criticalStyle.remove();
      }
    };
  }, []);

  return null; // This component doesn't render anything
};

export default HeroPerformanceOptimizer;
