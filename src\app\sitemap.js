import { blogPosts } from '@/data/blogPosts';

export default function sitemap() {
  const baseUrl = 'https://bakasana-travel.blog';
  const currentDate = new Date();
  
  // Main pages with enhanced metadata
  const mainPages = [
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 1.0,
      alternates: {
        languages: {
          pl: baseUrl,
          en: `${baseUrl}?lang=en`,
        },
      },
    },
    {
      url: `${baseUrl}/program`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.95,
      alternates: {
        languages: {
          pl: `${baseUrl}/program`,
          en: `${baseUrl}/program?lang=en`,
        },
      },
    },
    {
      url: `${baseUrl}/rezerwacja`,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 0.95,
      alternates: {
        languages: {
          pl: `${baseUrl}/rezerwacja`,
          en: `${baseUrl}/rezerwacja?lang=en`,
        },
      },
    },
    // SEO Landing Pages - Tier 1 Keywords
    {
      url: `${baseUrl}/retreaty-jogi-bali-2025`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.95,
      alternates: {
        languages: {
          pl: `${baseUrl}/retreaty-jogi-bali-2025`,
          en: `${baseUrl}/retreaty-jogi-bali-2025?lang=en`,
        },
      },
    },
    {
      url: `${baseUrl}/joga-sri-lanka-retreat`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.95,
      alternates: {
        languages: {
          pl: `${baseUrl}/joga-sri-lanka-retreat`,
          en: `${baseUrl}/joga-sri-lanka-retreat?lang=en`,
        },
      },
    },
    {
      url: `${baseUrl}/julia-jakubowicz-instruktor`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.95,
      alternates: {
        languages: {
          pl: `${baseUrl}/julia-jakubowicz-instruktor`,
          en: `${baseUrl}/julia-jakubowicz-instruktor?lang=en`,
        },
      },
    },
    {
      url: `${baseUrl}/yoga-retreat-z-polski`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.95,
      alternates: {
        languages: {
          pl: `${baseUrl}/yoga-retreat-z-polski`,
          en: `${baseUrl}/yoga-retreat-z-polski?lang=en`,
        },
      },
    },
    {
      url: `${baseUrl}/transformacyjne-podroze-azja`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.95,
      alternates: {
        languages: {
          pl: `${baseUrl}/transformacyjne-podroze-azja`,
          en: `${baseUrl}/transformacyjne-podroze-azja?lang=en`,
        },
      },
    },

    {
      url: `${baseUrl}/galeria`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.80,
      alternates: {
        languages: {
          pl: `${baseUrl}/galeria`,
          en: `${baseUrl}/galeria?lang=en`,
        },
      },
    },
    {
      url: `${baseUrl}/kontakt`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.75,
      alternates: {
        languages: {
          pl: `${baseUrl}/kontakt`,
          en: `${baseUrl}/kontakt?lang=en`,
        },
      },
    },
    {
      url: `${baseUrl}/blog`,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 0.90,
      alternates: {
        languages: {
          pl: `${baseUrl}/blog`,
          en: `${baseUrl}/blog?lang=en`,
        },
      },
    },
    {
      url: `${baseUrl}/zajecia-online`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.70,
      alternates: {
        languages: {
          pl: `${baseUrl}/zajecia-online`,
          en: `${baseUrl}/zajecia-online?lang=en`,
        },
      },
    },
    {
      url: `${baseUrl}/wellness`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.65,
      alternates: {
        languages: {
          pl: `${baseUrl}/wellness`,
          en: `${baseUrl}/wellness?lang=en`,
        },
      },
    },
    {
      url: `${baseUrl}/mapa`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.60,
      alternates: {
        languages: {
          pl: `${baseUrl}/mapa`,
          en: `${baseUrl}/mapa?lang=en`,
        },
      },
    },
    {
      url: `${baseUrl}/polityka-prywatnosci`,
      lastModified: new Date('2024-01-01'),
      changeFrequency: 'yearly',
      priority: 0.30,
    },
  ];

  // Blog posts with enhanced metadata
  const blogPages = blogPosts.map((post) => ({
    url: `${baseUrl}/blog/${post.slug}`,
    lastModified: new Date(post.dateModified || post.date),
    changeFrequency: 'monthly',
    priority: 0.80,
    alternates: {
      languages: {
        pl: `${baseUrl}/blog/${post.slug}`,
        en: `${baseUrl}/blog/${post.slug}?lang=en`,
      },
    },
  }));

  // Destination pages - only existing pages
  const destinationPages = [
    {
      url: `${baseUrl}/program?destination=bali`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.90,
    },
    {
      url: `${baseUrl}/program?destination=srilanka`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.90,
    },
  ];

  // Category pages
  const categories = ['bali', 'sri-lanka', 'joga', 'medytacja', 'ayurveda', 'wellness'];
  const categoryPages = categories.map((category) => ({
    url: `${baseUrl}/blog/kategoria/${category}`,
    lastModified: currentDate,
    changeFrequency: 'weekly',
    priority: 0.70,
    alternates: {
      languages: {
        pl: `${baseUrl}/blog/kategoria/${category}`,
        en: `${baseUrl}/blog/kategoria/${category}?lang=en`,
      },
    },
  }));

  return [
    ...mainPages,
    ...blogPages,
    ...destinationPages,
    ...categoryPages,
  ];
}