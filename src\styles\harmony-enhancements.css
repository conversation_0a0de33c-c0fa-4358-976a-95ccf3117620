/**
 * 🎼 HARMONY ENHANCEMENTS - Finalne <PERSON>
 * Dodatkowe style dla perfekcyjnej harmonii między elementami
 */

/* =============================================
   🎯 NAVBAR HARMONY FIXES
   ============================================= */

/* Zapewnienie, że navbar harmonizuje z hero */
.navbar-adaptive {
  transition: all 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
}

/* Na jasnym hero - navbar przezroczysty */
.navbar-light-mode {
  background: rgba(253, 252, 248, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(139, 115, 85, 0.1);
}

/* Po scrollu - navbar ciemny */
.navbar-scrolled {
  background: rgba(42, 39, 36, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Logo harmonizacja */
.navbar-light-mode a[href="/"] {
  color: var(--accent-earth);
}

.navbar-scrolled a[href="/"] {
  color: var(--light-bg);
}

/* =============================================
   🌊 HERO SECTION ENHANCEMENTS
   ============================================= */

/* Hero - używa oryginalnych stylów z hero.css */

/* =============================================
   📱 MOBILE OPTIMIZATIONS
   ============================================= */

@media (max-width: 768px) {
  /* Mobile navbar adjustments */
  .navbar-adaptive {
    padding: 0 1rem;
  }
  
  .navbar-light-mode {
    background: rgba(253, 252, 248, 0.95);
  }
  
  /* Mobile hero - używa oryginalnych stylów */
  
  /* Mobile floating elements - smaller and fewer */
  .floating-element {
    opacity: 0.5;
  }
  
  .floating-element:nth-child(n+3) {
    display: none; /* Hide third floating element on mobile */
  }
}

/* =============================================
   🎨 COLOR HARMONY IMPROVEMENTS
   ============================================= */

/* Ensure consistent color usage across components */
.section-title-enhanced {
  color: var(--light-text);
}

.section-subtitle-enhanced {
  color: var(--accent-earth);
}

.body-text-enhanced {
  color: var(--charcoal-light);
}

/* Card harmonization */
.card-bakasana {
  background: var(--light-surface);
  border: 1px solid rgba(139, 115, 85, 0.1);
}

.card-bakasana:hover {
  border-color: rgba(139, 115, 85, 0.2);
  box-shadow: 0 8px 25px rgba(139, 115, 85, 0.15);
}

/* Button harmonization */
.btn-bakasana {
  border-color: var(--accent-earth);
  color: var(--accent-earth);
}

.btn-bakasana:hover {
  color: var(--light-bg);
}

/* Link harmonization */
.link-bakasana {
  color: var(--accent-earth);
}

.link-bakasana:hover {
  color: var(--accent-warm);
}

/* =============================================
   🌟 SECTION SPACING HARMONY
   ============================================= */

/* Consistent section spacing */
.rhythm-section {
  padding: 6rem 0;
}

.rhythm-section:first-child {
  padding-top: 0;
}

.rhythm-section:last-child {
  padding-bottom: 8rem;
}

@media (max-width: 768px) {
  .rhythm-section {
    padding: 4rem 0;
  }
  
  .rhythm-section:last-child {
    padding-bottom: 6rem;
  }
}

/* =============================================
   ✨ MICRO-INTERACTIONS HARMONY
   ============================================= */

/* Consistent hover states */
.card-bakasana,
.btn-bakasana,
.link-bakasana {
  transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
}

/* Focus states harmony */
.btn-bakasana:focus-visible,
.link-bakasana:focus-visible,
.card-bakasana:focus-visible {
  outline: 2px solid var(--accent-warm);
  outline-offset: 4px;
}

/* =============================================
   🎭 ANIMATION HARMONY
   ============================================= */

/* Consistent animation timing */
.stagger-animation {
  animation-duration: 0.8s;
  animation-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
  animation-fill-mode: both;
}

/* Breathing elements harmony */
.breathing-element {
  animation-duration: 6s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
}

/* Floating elements harmony */
.floating-element {
  animation-duration: 8s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
}

/* =============================================
   🖼️ IMAGE HARMONY
   ============================================= */

/* All images get consistent treatment */
img {
  transition: filter var(--entrance-duration, 0.6s) ease;
  will-change: filter; /* GPU acceleration for smooth transitions */
}

/* Hero images */
.image-hero {
  filter: 
    contrast(0.92)
    brightness(1.08)
    saturate(0.85);
}

/* Regular images */
.image-bakasana {
  filter: 
    contrast(0.95)
    brightness(1.05)
    saturate(0.9);
}

/* Hover states for images */
.image-bakasana:hover,
.image-hero:hover {
  filter: 
    contrast(1)
    brightness(1.08)
    saturate(0.95)
    sepia(0.05);
}

/* =============================================
   📐 LAYOUT HARMONY
   ============================================= */

/* Container consistency - UNIFIED with core.css */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem; /* 16px - follows 8px grid */
}

@media (min-width: 768px) {
  .container {
    padding: 0 2rem; /* 32px - follows 8px grid */
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 3rem; /* 48px - follows 8px grid */
  }
}

/* Grid harmony */
.grid {
  gap: 2rem;
}

@media (max-width: 768px) {
  .grid {
    gap: 1.5rem;
  }
}

/* =============================================
   🎯 TYPOGRAPHY HARMONY
   ============================================= */

/* Consistent font loading */
.font-cormorant {
  font-family: 'Cormorant Garamond', serif;
  font-display: swap;
}

.font-inter {
  font-family: 'Inter', sans-serif;
  font-display: swap;
}

/* Consistent line heights */
h1, h2, h3, .hero-title-enhanced, .section-title-enhanced {
  line-height: 1.1;
}

p, .body-text-enhanced {
  line-height: 1.7;
}

/* =============================================
   🌙 DARK MODE PREPARATIONS
   ============================================= */

/* Variables for future dark mode */
[data-theme="dark"] {
  --light-bg: var(--dark-bg);
  --light-surface: var(--dark-surface);
  --light-text: var(--dark-text);
}

[data-theme="dark"] .navbar-light-mode {
  background: rgba(26, 26, 26, 0.95);
  color: var(--dark-text);
}

/* Dark mode hero - używa oryginalnych stylów */

/* =============================================
   🔧 PERFORMANCE OPTIMIZATIONS
   ============================================= */

/* GPU acceleration for animations */
.floating-element,
.breathing-element,
.stagger-animation {
  transform: translateZ(0);
  will-change: transform;
}

/* Optimize repaints */
.navbar-adaptive {
  will-change: background-color, backdrop-filter;
}

/* =============================================
   🎨 FINAL POLISH
   ============================================= */

/* Smooth everything */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Perfect scrolling */
html {
  scroll-behavior: smooth;
  scroll-padding-top: 100px;
}

/* Remove default margins */
h1, h2, h3, h4, h5, h6, p {
  margin: 0;
}

/* Perfect box model */
*, *::before, *::after {
  box-sizing: border-box;
}

/* =============================================
   ACCESSIBILITY FINAL TOUCHES
   ============================================= */

/* Respect user preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  html {
    scroll-behavior: auto;
  }
}

/* High contrast support */
@media (prefers-contrast: high) {
  .navbar-light-mode {
    background: rgba(255, 255, 255, 0.98);
    border-bottom: 2px solid #000;
  }
  
  .navbar-scrolled {
    background: rgba(0, 0, 0, 0.98);
    border-bottom: 2px solid #fff;
  }
}