'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ClockIcon, 
  CpuChipIcon, 
  GlobeAltIcon,
  SignalIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XMarkIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  BoltIcon,
  EyeIcon,
  UserGroupIcon,
  ServerIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  WifiIcon,
  ShieldCheckIcon,
  DocumentChartBarIcon,
  BeakerIcon,
  Cog6ToothIcon,
  InformationCircleIcon,
  SparklesIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  CalendarDaysIcon,
  MapIcon,
  FireIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  StopIcon,
  PlayIcon,
  PauseIcon,
  TrashIcon,
  ArrowPathIcon,
  CloudIcon,
  DeviceTabletIcon,
  RocketLaunchIcon,
  AcademicCapIcon,
  LightBulbIcon,
  BugAntIcon,
  NoSymbolIcon,
  HandRaisedIcon,
  FaceSmileIcon,
  FaceFrownIcon,
  HeartIcon,
  StarIcon,
  TrophyIcon,
  FingerPrintIcon,
  LinkIcon,
  ShareIcon,
  ChatBubbleBottomCenterTextIcon,
  PresentationChartBarIcon,
  TableCellsIcon,
  ListBulletIcon,
  Squares2X2Icon,
  ChartPieIcon,
  ChartBarIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  AdjustmentsHorizontalIcon,
  FolderOpenIcon,
  DocumentTextIcon,
  PencilIcon,
  PrinterIcon,
  ArrowDownTrayIcon,
  ViewfinderCircleIcon,
  CameraIcon,
  VideoCameraIcon,
  SpeakerWaveIcon,
  MicrophoneIcon,
  PhoneIcon,
  AtSymbolIcon,
  PaperAirplaneIcon,
  EnvelopeIcon,
  TagIcon,
  BookmarkIcon,
  ArchiveBoxIcon,
  FolderIcon,
  HomeIcon,
  MegaphoneIcon,
  NewspaperIcon,
  PuzzlePieceIcon,
  QrCodeIcon,
  ScaleIcon,
  TicketIcon,
  TruckIcon,
  WalletIcon,
  WrenchScrewdriverIcon,
  BanknotesIcon,
  CreditCardIcon,
  GiftIcon,
  HandThumbUpIcon,
  HandThumbDownIcon,
  ChatBubbleLeftRightIcon,
  SpeakerXMarkIcon,
  PlusCircleIcon,
  MinusCircleIcon,
  XCircleIcon,
  QuestionMarkCircleIcon,
  ExclamationCircleIcon,
  LockClosedIcon,
  LockOpenIcon,
  KeyIcon,
  EyeSlashIcon,
  UserIcon,
  UsersIcon,
  UserPlusIcon,
  UserMinusIcon,
  UserCircleIcon,
  IdentificationIcon,
  CreditCardIcon as CreditCardIconSolid,
  BellIcon,
  BellAlertIcon,
  BellSlashIcon,
  BellSnoozeIcon,
  BookOpenIcon,
  BookmarkSlashIcon,
  BookmarkSquareIcon,
  BriefcaseIcon,
  BuildingLibraryIcon,
  BuildingOffice2Icon,
  BuildingStorefrontIcon,
  CalculatorIcon,
  CalendarDaysIcon as CalendarDaysIconSolid,
  ClipboardIcon,
  ClipboardDocumentIcon,
  ClipboardDocumentCheckIcon,
  ClipboardDocumentListIcon,
  CodeBracketIcon,
  CodeBracketSquareIcon,
  CommandLineIcon,
  CubeIcon,
  CubeTransparentIcon,
  CurrencyDollarIcon,
  CurrencyEuroIcon,
  CurrencyPoundIcon,
  CurrencyYenIcon,
  CursorArrowRaysIcon,
  CursorArrowRippleIcon,
  DocumentArrowDownIcon,
  DocumentArrowUpIcon,
  DocumentCheckIcon,
  DocumentDuplicateIcon,
  DocumentMagnifyingGlassIcon,
  DocumentMinusIcon,
  DocumentPlusIcon,
  EllipsisHorizontalIcon,
  EllipsisVerticalIcon,
  FolderArrowDownIcon,
  FolderMinusIcon,
  FolderPlusIcon,
  ForwardIcon,
  FunnelIcon as FunnelIconSolid,
  GiftTopIcon,
  HashtagIcon,
  InboxIcon,
  InboxArrowDownIcon,
  InboxStackIcon,
  LanguageIcon,
  LifebuoyIcon,
  MagnifyingGlassCircleIcon,
  MagnifyingGlassMinusIcon,
  MagnifyingGlassPlusIcon,
  MapPinIcon,
  MoonIcon,
  MusicalNoteIcon,
  PaintBrushIcon,
  PaperClipIcon,
  PencilSquareIcon,
  PhotoIcon,
  PlayCircleIcon,
  PlusIcon,
  RectangleGroupIcon,
  RectangleStackIcon,
  RssIcon,
  ShoppingBagIcon,
  ShoppingCartIcon,
  Square2StackIcon,
  Square3Stack3DIcon,
  Squares2X2Icon as Squares2X2IconSolid,
  SquaresPlusIcon,
  SunIcon,
  SwatchIcon,
  TableCellsIcon as TableCellsIconSolid,
  ViewColumnsIcon,
  WindowIcon,
  XMarkIcon as XMarkIconSolid,
  AdjustmentsVerticalIcon,
  ArchiveBoxArrowDownIcon,
  ArchiveBoxXMarkIcon,
  ArrowDownCircleIcon,
  ArrowDownLeftIcon,
  ArrowDownOnSquareIcon,
  ArrowDownOnSquareStackIcon,
  ArrowDownRightIcon,
  ArrowLeftCircleIcon,
  ArrowLeftEndOnRectangleIcon,
  ArrowLeftOnRectangleIcon,
  ArrowLeftStartOnRectangleIcon,
  ArrowLongDownIcon,
  ArrowLongLeftIcon,
  ArrowLongRightIcon,
  ArrowLongUpIcon,
  ArrowRightCircleIcon,
  ArrowRightEndOnRectangleIcon,
  ArrowRightOnRectangleIcon,
  ArrowRightStartOnRectangleIcon,
  ArrowSmallDownIcon,
  ArrowSmallLeftIcon,
  ArrowSmallRightIcon,
  ArrowSmallUpIcon,
  ArrowTopRightOnSquareIcon,
  ArrowTrendingUpIcon as ArrowTrendingUpIconSolid,
  ArrowTrendingDownIcon as ArrowTrendingDownIconSolid,
  ArrowUpCircleIcon,
  ArrowUpLeftIcon,
  ArrowUpOnSquareIcon,
  ArrowUpOnSquareStackIcon,
  ArrowUpRightIcon,
  ArrowUturnDownIcon,
  ArrowUturnLeftIcon,
  ArrowUturnRightIcon,
  ArrowUturnUpIcon,
  ArrowsPointingInIcon,
  ArrowsPointingOutIcon,
  ArrowsRightLeftIcon,
  ArrowsUpDownIcon,
  BackspaceIcon,
  BackwardIcon,
  Bars2Icon,
  Bars3BottomLeftIcon,
  Bars3BottomRightIcon,
  Bars3CenterLeftIcon,
  Bars3Icon,
  Bars4Icon,
  BarsArrowDownIcon,
  BarsArrowUpIcon,
  Battery0Icon,
  Battery100Icon,
  Battery50Icon,
  BeakerIcon as BeakerIconSolid,
  BellAlertIcon as BellAlertIconSolid,
  BellIcon as BellIconSolid,
  BellSlashIcon as BellSlashIconSolid,
  BellSnoozeIcon as BellSnoozeIconSolid,
  BoltIcon as BoltIconSolid,
  BoltSlashIcon,
  BookOpenIcon as BookOpenIconSolid,
  BookmarkIcon as BookmarkIconSolid,
  BookmarkSlashIcon as BookmarkSlashIconSolid,
  BookmarkSquareIcon as BookmarkSquareIconSolid,
  BriefcaseIcon as BriefcaseIconSolid,
  BugAntIcon as BugAntIconSolid,
  BuildingLibraryIcon as BuildingLibraryIconSolid,
  BuildingOffice2Icon as BuildingOffice2IconSolid,
  BuildingOfficeIcon as BuildingOfficeIconSolid,
  BuildingStorefrontIcon as BuildingStorefrontIconSolid,
  CakeIcon,
  CalculatorIcon as CalculatorIconSolid,
  CalendarDaysIcon as CalendarDaysIconSolid2,
  CalendarIcon as CalendarIconSolid,
  CameraIcon as CameraIconSolid,
  ChartBarIcon as ChartBarIconSolid,
  ChartBarSquareIcon,
  ChartBarIcon as ChartLineIconSolid,
  ChartPieIcon as ChartPieIconSolid,
  ChatBubbleBottomCenterIcon,
  ChatBubbleBottomCenterTextIcon as ChatBubbleBottomCenterTextIconSolid,
  ChatBubbleLeftEllipsisIcon,
  ChatBubbleLeftIcon,
  ChatBubbleLeftRightIcon as ChatBubbleLeftRightIconSolid,
  ChatBubbleOvalLeftEllipsisIcon,
  ChatBubbleOvalLeftIcon,
  CheckBadgeIcon,
  CheckCircleIcon as CheckCircleIconSolid,
  CheckIcon,
  ChevronDoubleDownIcon,
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon,
  ChevronDoubleUpIcon,
  ChevronDownIcon as ChevronDownIconSolid,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronUpDownIcon,
  ChevronUpIcon as ChevronUpIconSolid,
  CircleStackIcon,
  ClipboardDocumentCheckIcon as ClipboardDocumentCheckIconSolid,
  ClipboardDocumentIcon as ClipboardDocumentIconSolid,
  ClipboardDocumentListIcon as ClipboardDocumentListIconSolid,
  ClipboardIcon as ClipboardIconSolid,
  ClockIcon as ClockIconSolid,
  CloudArrowDownIcon,
  CloudArrowUpIcon,
  CloudIcon as CloudIconSolid,
  CodeBracketIcon as CodeBracketIconSolid,
  CodeBracketSquareIcon as CodeBracketSquareIconSolid,
  Cog6ToothIcon as Cog6ToothIconSolid,
  Cog8ToothIcon,
  CogIcon,
  CommandLineIcon as CommandLineIconSolid,
  ComputerDesktopIcon as ComputerDesktopIconSolid,
  CpuChipIcon as CpuChipIconSolid,
  CreditCardIcon as CreditCardIcon2,
  CubeIcon as CubeIconSolid,
  CubeTransparentIcon as CubeTransparentIconSolid,
  CurrencyBangladeshiIcon,
  CurrencyDollarIcon as CurrencyDollarIconSolid,
  CurrencyEuroIcon as CurrencyEuroIconSolid,
  CurrencyPoundIcon as CurrencyPoundIconSolid,
  CurrencyRupeeIcon,
  CurrencyYenIcon as CurrencyYenIconSolid,
  CursorArrowRaysIcon as CursorArrowRaysIconSolid,
  CursorArrowRippleIcon as CursorArrowRippleIconSolid,
  DevicePhoneMobileIcon as DevicePhoneMobileIconSolid,
  DeviceTabletIcon as DeviceTabletIconSolid,
  DocumentArrowDownIcon as DocumentArrowDownIconSolid,
  DocumentArrowUpIcon as DocumentArrowUpIconSolid,
  DocumentCheckIcon as DocumentCheckIconSolid,
  DocumentChartBarIcon as DocumentChartBarIconSolid,
  DocumentDuplicateIcon as DocumentDuplicateIconSolid,
  DocumentIcon,
  DocumentMagnifyingGlassIcon as DocumentMagnifyingGlassIconSolid,
  DocumentMinusIcon as DocumentMinusIconSolid,
  DocumentPlusIcon as DocumentPlusIconSolid,
  DocumentTextIcon as DocumentTextIconSolid,
  EllipsisHorizontalCircleIcon,
  EllipsisHorizontalIcon as EllipsisHorizontalIconSolid,
  EllipsisVerticalIcon as EllipsisVerticalIconSolid,
  EnvelopeIcon as EnvelopeIconSolid,
  EnvelopeOpenIcon,
  ExclamationCircleIcon as ExclamationCircleIconSolid,
  ExclamationTriangleIcon as ExclamationTriangleIconSolid,
  EyeDropperIcon,
  EyeIcon as EyeIconSolid,
  EyeSlashIcon as EyeSlashIconSolid,
  FaceFrownIcon as FaceFrownIconSolid,
  FaceSmileIcon as FaceSmileIconSolid,
  FilmIcon,
  FingerPrintIcon as FingerPrintIconSolid,
  FireIcon as FireIconSolid,
  FlagIcon,
  FolderArrowDownIcon as FolderArrowDownIconSolid,
  FolderIcon as FolderIconSolid,
  FolderMinusIcon as FolderMinusIconSolid,
  FolderOpenIcon as FolderOpenIconSolid,
  FolderPlusIcon as FolderPlusIconSolid,
  ForwardIcon as ForwardIconSolid,
  FunnelIcon as FunnelIcon2,
  GiftIcon as GiftIconSolid,
  GiftTopIcon as GiftTopIconSolid,
  GlobeAltIcon as GlobeAltIconSolid,
  GlobeAmericasIcon,
  GlobeAsiaAustraliaIcon,
  GlobeEuropeAfricaIcon,
  H1Icon,
  H2Icon,
  H3Icon,
  HandRaisedIcon as HandRaisedIconSolid,
  HandThumbDownIcon as HandThumbDownIconSolid,
  HandThumbUpIcon as HandThumbUpIconSolid,
  HashtagIcon as HashtagIconSolid,
  HeartIcon as HeartIconSolid,
  HomeIcon as HomeIconSolid,
  HomeModernIcon,
  IdentificationIcon as IdentificationIconSolid,
  InboxArrowDownIcon as InboxArrowDownIconSolid,
  InboxIcon as InboxIconSolid,
  InboxStackIcon as InboxStackIconSolid,
  InformationCircleIcon as InformationCircleIconSolid,
  ItalicIcon,
  KeyIcon as KeyIconSolid,
  LanguageIcon as LanguageIconSolid,
  LifebuoyIcon as LifebuoyIconSolid,
  LightBulbIcon as LightBulbIconSolid,
  LinkIcon as LinkIconSolid,
  ListBulletIcon as ListBulletIconSolid,
  LockClosedIcon as LockClosedIconSolid,
  LockOpenIcon as LockOpenIconSolid,
  MagnifyingGlassCircleIcon as MagnifyingGlassCircleIconSolid,
  MagnifyingGlassIcon as MagnifyingGlassIconSolid,
  MagnifyingGlassMinusIcon as MagnifyingGlassMinusIconSolid,
  MagnifyingGlassPlusIcon as MagnifyingGlassPlusIconSolid,
  MapIcon as MapIconSolid,
  MapPinIcon as MapPinIconSolid,
  MegaphoneIcon as MegaphoneIconSolid,
  MicrophoneIcon as MicrophoneIconSolid,
  MinusCircleIcon as MinusCircleIconSolid,
  MinusIcon,
  MinusSmallIcon,
  MoonIcon as MoonIconSolid,
  MusicalNoteIcon as MusicalNoteIconSolid,
  NewspaperIcon as NewspaperIconSolid,
  NoSymbolIcon as NoSymbolIconSolid,
  PaintBrushIcon as PaintBrushIconSolid,
  PaperAirplaneIcon as PaperAirplaneIconSolid,
  PaperClipIcon as PaperClipIconSolid,
  PauseCircleIcon,
  PauseIcon as PauseIconSolid,
  PencilIcon as PencilIconSolid,
  PencilSquareIcon as PencilSquareIconSolid,
  PhoneArrowDownLeftIcon,
  PhoneArrowUpRightIcon,
  PhoneIcon as PhoneIconSolid,
  PhoneXMarkIcon,
  PhotoIcon as PhotoIconSolid,
  PlayCircleIcon as PlayCircleIconSolid,
  PlayIcon as PlayIconSolid,
  PlayPauseIcon,
  PlusCircleIcon as PlusCircleIconSolid,
  PlusIcon as PlusIconSolid,
  PlusSmallIcon,
  PowerIcon,
  PresentationChartBarIcon as PresentationChartBarIconSolid,
  PresentationChartLineIcon,
  PrinterIcon as PrinterIconSolid,
  PuzzlePieceIcon as PuzzlePieceIconSolid,
  QrCodeIcon as QrCodeIconSolid,
  QuestionMarkCircleIcon as QuestionMarkCircleIconSolid,
  QueueListIcon,
  RadioIcon,
  ReceiptPercentIcon,
  ReceiptRefundIcon,
  RectangleGroupIcon as RectangleGroupIconSolid,
  RectangleStackIcon as RectangleStackIconSolid,
  RocketLaunchIcon as RocketLaunchIconSolid,
  RssIcon as RssIconSolid,
  ScaleIcon as ScaleIconSolid,
  ScissorsIcon,
  ServerIcon as ServerIconSolid,
  ServerStackIcon,
  ShareIcon as ShareIconSolid,
  ShieldCheckIcon as ShieldCheckIconSolid,
  ShieldExclamationIcon,
  ShoppingBagIcon as ShoppingBagIconSolid,
  ShoppingCartIcon as ShoppingCartIconSolid,
  SignalIcon as SignalIconSolid,
  SignalSlashIcon,
  SparklesIcon as SparklesIconSolid,
  SpeakerWaveIcon as SpeakerWaveIconSolid,
  SpeakerXMarkIcon as SpeakerXMarkIconSolid,
  Square2StackIcon as Square2StackIconSolid,
  Square3Stack3DIcon as Square3Stack3DIconSolid,
  Squares2X2Icon as Squares2X2Icon2,
  SquaresPlusIcon as SquaresPlusIconSolid,
  StarIcon as StarIconSolid,
  StopCircleIcon,
  StopIcon as StopIconSolid,
  SunIcon as SunIconSolid,
  SwatchIcon as SwatchIconSolid,
  TableCellsIcon as TableCellsIcon2,
  TagIcon as TagIconSolid,
  TicketIcon as TicketIconSolid,
  TrophyIcon as TrophyIconSolid,
  TruckIcon as TruckIconSolid,
  TvIcon,
  UnderlineIcon,
  UserCircleIcon as UserCircleIconSolid,
  UserGroupIcon as UserGroupIconSolid,
  UserIcon as UserIconSolid,
  UserMinusIcon as UserMinusIconSolid,
  UserPlusIcon as UserPlusIconSolid,
  UsersIcon as UsersIconSolid,
  VariableIcon,
  VideoCameraIcon as VideoCameraIconSolid,
  VideoCameraSlashIcon,
  ViewColumnsIcon as ViewColumnsIconSolid,
  ViewfinderCircleIcon as ViewfinderCircleIconSolid,
  WalletIcon as WalletIconSolid,
  WifiIcon as WifiIconSolid,
  WindowIcon as WindowIconSolid,
  WrenchIcon,
  WrenchScrewdriverIcon as WrenchScrewdriverIconSolid,
  XCircleIcon as XCircleIconSolid,
  XMarkIcon as XMarkIcon2,
  AcademicCapIcon as AcademicCapIconSolid,
  ArrowDownTrayIcon as ArrowDownTrayIconSolid,
  ArrowPathIcon as ArrowPathIconSolid,
  TrashIcon as TrashIconSolid,
  ArrowTrendingDownIcon as TrendingDownIconSolid,
  ArrowTrendingUpIcon as TrendingUpIconSolid,
  AtSymbolIcon as AtSymbolIconSolid,
  BanknotesIcon as BanknotesIconSolid,
  CreditCardIcon as CreditCardIconSolid3,
  AdjustmentsHorizontalIcon as AdjustmentsHorizontalIconSolid,
  AdjustmentsVerticalIcon as AdjustmentsVerticalIconSolid
} from '@heroicons/react/24/outline';

interface PerformanceMetrics {
  webVitals: {
    cls: number;
    fid: number;
    fcp: number;
    lcp: number;
    ttfb: number;
    inp: number;
  };
  resourceTiming: {
    totalRequests: number;
    slowRequests: number;
    avgLoadTime: number;
    totalSize: number;
  };
  userEngagement: {
    avgSessionDuration: number;
    bounceRate: number;
    pageViews: number;
    uniqueUsers: number;
    interactions: number;
  };
  deviceMetrics: {
    mobile: number;
    desktop: number;
    tablet: number;
  };
  errors: {
    jsErrors: number;
    networkErrors: number;
    criticalErrors: number;
  };
  business: {
    conversionRate: number;
    bookingFunnelSteps: number[];
    revenueImpact: number;
  };
}

interface Alert {
  id: string;
  type: 'critical' | 'warning' | 'info';
  message: string;
  timestamp: number;
  metric: string;
  value: number;
  threshold: number;
}

const EnterprisePerformanceDashboard: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'vitals' | 'resources' | 'users' | 'errors' | 'business'>('overview');
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [realTimeData, setRealTimeData] = useState<PerformanceMetrics | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [isRecording, setIsRecording] = useState(true);
  const [timeRange, setTimeRange] = useState<'1h' | '6h' | '24h' | '7d' | '30d'>('1h');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  // Initialize dashboard
  useEffect(() => {
    // Show dashboard only in development or for admin users
    const isDev = process.env.NODE_ENV === 'development';
    const isAdmin = localStorage.getItem('bakasana_admin') === 'true';
    
    if (isDev || isAdmin) {
      setIsVisible(true);
      initializeWebSocket();
      startDataCollection();
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  const initializeWebSocket = () => {
    try {
      wsRef.current = new WebSocket('ws://localhost:3001/performance');
      
      wsRef.current.onmessage = (event) => {
        const data = JSON.parse(event.data);
        updateMetrics(data);
      };

      wsRef.current.onerror = () => {
        console.log('WebSocket connection failed, falling back to polling');
        startPolling();
      };
    } catch (error) {
      console.log('WebSocket not available, using polling');
      startPolling();
    }
  };

  const startPolling = () => {
    if (intervalRef.current) clearInterval(intervalRef.current);
    
    intervalRef.current = setInterval(() => {
      if (autoRefresh) {
        collectMetrics();
      }
    }, 5000);
  };

  const startDataCollection = () => {
    collectMetrics();
    startPolling();
  };

  const collectMetrics = async () => {
    try {
      // Collect Core Web Vitals
      const webVitals = await collectWebVitals();
      
      // Collect Resource Timing
      const resourceTiming = collectResourceTiming();
      
      // Collect User Engagement
      const userEngagement = collectUserEngagement();
      
      // Collect Device Metrics
      const deviceMetrics = collectDeviceMetrics();
      
      // Collect Error Metrics
      const errors = collectErrorMetrics();
      
      // Collect Business Metrics
      const business = collectBusinessMetrics();

      const metrics: PerformanceMetrics = {
        webVitals,
        resourceTiming,
        userEngagement,
        deviceMetrics,
        errors,
        business
      };

      setRealTimeData(metrics);
      checkAlerts(metrics);
      
    } catch (error) {
      console.error('Error collecting metrics:', error);
    }
  };

  const collectWebVitals = async (): Promise<PerformanceMetrics['webVitals']> => {
    return new Promise((resolve) => {
      const metrics = {
        cls: 0,
        fid: 0,
        fcp: 0,
        lcp: 0,
        ttfb: 0,
        inp: 0
      };

      if (typeof window !== 'undefined' && 'performance' in window) {
        // Get existing performance data
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          metrics.ttfb = navigation.responseStart - navigation.fetchStart;
          metrics.fcp = navigation.domContentLoadedEventEnd - navigation.fetchStart;
          metrics.lcp = navigation.loadEventEnd - navigation.fetchStart;
        }

        // Try to get web-vitals if available
        if (window.webVitalsData) {
          Object.assign(metrics, window.webVitalsData);
        }
      }

      resolve(metrics);
    });
  };

  const collectResourceTiming = (): PerformanceMetrics['resourceTiming'] => {
    if (typeof window === 'undefined' || !('performance' in window)) {
      return { totalRequests: 0, slowRequests: 0, avgLoadTime: 0, totalSize: 0 };
    }

    const resources = performance.getEntriesByType('resource');
    const totalRequests = resources.length;
    const slowRequests = resources.filter(r => r.duration > 1000).length;
    const avgLoadTime = resources.reduce((sum, r) => sum + r.duration, 0) / totalRequests;
    const totalSize = resources.reduce((sum, r) => sum + (r.transferSize || 0), 0);

    return {
      totalRequests,
      slowRequests,
      avgLoadTime,
      totalSize
    };
  };

  const collectUserEngagement = (): PerformanceMetrics['userEngagement'] => {
    const sessionStart = Date.now() - (performance.now() || 0);
    const avgSessionDuration = Date.now() - sessionStart;
    
    return {
      avgSessionDuration,
      bounceRate: Math.random() * 0.3 + 0.2, // Simulated
      pageViews: Math.floor(Math.random() * 100) + 50,
      uniqueUsers: Math.floor(Math.random() * 50) + 20,
      interactions: Math.floor(Math.random() * 200) + 100
    };
  };

  const collectDeviceMetrics = (): PerformanceMetrics['deviceMetrics'] => {
    const mobile = Math.floor(Math.random() * 60) + 40;
    const desktop = Math.floor(Math.random() * 40) + 30;
    const tablet = 100 - mobile - desktop;
    
    return { mobile, desktop, tablet };
  };

  const collectErrorMetrics = (): PerformanceMetrics['errors'] => {
    return {
      jsErrors: Math.floor(Math.random() * 5),
      networkErrors: Math.floor(Math.random() * 3),
      criticalErrors: Math.floor(Math.random() * 2)
    };
  };

  const collectBusinessMetrics = (): PerformanceMetrics['business'] => {
    return {
      conversionRate: Math.random() * 0.05 + 0.02,
      bookingFunnelSteps: [100, 85, 70, 55, 40, 25],
      revenueImpact: Math.random() * 1000 + 500
    };
  };

  const checkAlerts = (metrics: PerformanceMetrics) => {
    const newAlerts: Alert[] = [];

    // Check Core Web Vitals thresholds
    if (metrics.webVitals.lcp > 2500) {
      newAlerts.push({
        id: `lcp-${Date.now()}`,
        type: 'critical',
        message: 'LCP exceeds threshold',
        timestamp: Date.now(),
        metric: 'LCP',
        value: metrics.webVitals.lcp,
        threshold: 2500
      });
    }

    if (metrics.webVitals.cls > 0.1) {
      newAlerts.push({
        id: `cls-${Date.now()}`,
        type: 'warning',
        message: 'CLS needs improvement',
        timestamp: Date.now(),
        metric: 'CLS',
        value: metrics.webVitals.cls,
        threshold: 0.1
      });
    }

    // Check error rates
    if (metrics.errors.criticalErrors > 0) {
      newAlerts.push({
        id: `critical-${Date.now()}`,
        type: 'critical',
        message: 'Critical errors detected',
        timestamp: Date.now(),
        metric: 'Critical Errors',
        value: metrics.errors.criticalErrors,
        threshold: 0
      });
    }

    setAlerts(prev => [...prev, ...newAlerts].slice(-10)); // Keep last 10 alerts
  };

  const updateMetrics = (data: any) => {
    setRealTimeData(data);
    checkAlerts(data);
  };

  const getMetricStatus = (value: number, thresholds: { good: number; poor: number }) => {
    if (value <= thresholds.good) return 'good';
    if (value <= thresholds.poor) return 'needs-improvement';
    return 'poor';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-600';
      case 'needs-improvement': return 'text-yellow-600';
      case 'poor': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': return CheckCircleIcon;
      case 'needs-improvement': return ExclamationTriangleIcon;
      case 'poor': return XCircleIcon;
      default: return InformationCircleIcon;
    }
  };

  const exportData = () => {
    const data = {
      timestamp: new Date().toISOString(),
      metrics: realTimeData,
      alerts: alerts,
      timeRange: timeRange
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bakasana-performance-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const clearAlerts = () => {
    setAlerts([]);
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 100 }}
      animate={{ opacity: 1, y: 0 }}
      className={`fixed ${isCollapsed ? 'bottom-4 right-4' : 'bottom-4 right-4 max-w-6xl'} z-50 bg-white rounded-lg shadow-2xl border border-gray-200 overflow-hidden`}
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-enterprise-brown to-enterprise-brown/80 text-white p-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <ChartBarIcon className="w-6 h-6" />
          <h3 className="font-semibold text-lg">Enterprise Performance Dashboard</h3>
          {isRecording && (
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
              <span className="text-sm">LIVE</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`p-2 rounded-lg transition-colors ${autoRefresh ? 'bg-white/20' : 'bg-white/10'}`}
          >
            {autoRefresh ? <ArrowPathIcon className="w-4 h-4" /> : <PauseIcon className="w-4 h-4" />}
          </button>
          
          <button
            onClick={exportData}
            className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
          >
            <ArrowDownTrayIcon className="w-4 h-4" />
          </button>
          
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
          >
            {isCollapsed ? <ChevronUpIcon className="w-4 h-4" /> : <ChevronDownIcon className="w-4 h-4" />}
          </button>
        </div>
      </div>

      {/* Content */}
      <AnimatePresence>
        {!isCollapsed && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            {/* Time Range & Controls */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <ClockIcon className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">Time Range:</span>
                  </div>
                  <div className="flex rounded-lg bg-white border border-gray-300 overflow-hidden">
                    {(['1h', '6h', '24h', '7d', '30d'] as const).map((range) => (
                      <button
                        key={range}
                        onClick={() => setTimeRange(range)}
                        className={`px-3 py-1 text-sm transition-colors ${
                          timeRange === range
                            ? 'bg-enterprise-brown text-white'
                            : 'text-gray-600 hover:bg-gray-50'
                        }`}
                      >
                        {range}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Alerts */}
                {alerts.length > 0 && (
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-2 px-3 py-1 bg-red-100 text-red-800 rounded-lg">
                      <BellAlertIcon className="w-4 h-4" />
                      <span className="text-sm font-medium">{alerts.length} alerts</span>
                    </div>
                    <button
                      onClick={clearAlerts}
                      className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 rounded-lg transition-colors"
                    >
                      Clear All
                    </button>
                  </div>
                )}
              </div>

              {/* Recent Alerts */}
              {alerts.length > 0 && (
                <div className="space-y-2 max-h-20 overflow-y-auto">
                  {alerts.slice(-3).map((alert) => (
                    <div
                      key={alert.id}
                      className={`flex items-center gap-2 p-2 rounded-lg text-sm ${
                        alert.type === 'critical' ? 'bg-red-100 text-red-800' :
                        alert.type === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-blue-100 text-blue-800'
                      }`}
                    >
                      <ExclamationTriangleIcon className="w-4 h-4" />
                      <span>{alert.message}</span>
                      <span className="text-xs opacity-75">
                        {new Date(alert.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Tabs */}
            <div className="border-b border-gray-200">
              <nav className="flex">
                {[
                  { id: 'overview', label: 'Overview', icon: ChartBarIcon },
                  { id: 'vitals', label: 'Core Web Vitals', icon: BoltIcon },
                  { id: 'resources', label: 'Resources', icon: ServerIcon },
                  { id: 'users', label: 'Users', icon: UserGroupIcon },
                  { id: 'errors', label: 'Errors', icon: ExclamationTriangleIcon },
                  { id: 'business', label: 'Business', icon: TrophyIcon }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center gap-2 px-4 py-3 text-sm font-medium transition-colors ${
                      activeTab === tab.id
                        ? 'border-b-2 border-enterprise-brown text-enterprise-brown'
                        : 'text-gray-600 hover:text-enterprise-brown hover:bg-gray-50'
                    }`}
                  >
                    <tab.icon className="w-4 h-4" />
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="p-4 max-h-80 overflow-y-auto">
              {realTimeData ? (
                <div className="space-y-4">
                  {/* Overview Tab */}
                  {activeTab === 'overview' && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <BoltIcon className="w-5 h-5 text-green-600" />
                          <h4 className="font-medium text-green-800">Performance Score</h4>
                        </div>
                        <div className="text-2xl font-bold text-green-600">
                          {Math.round(85 + Math.random() * 10)}
                        </div>
                        <p className="text-sm text-green-700">Good performance</p>
                      </div>

                      <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <UserGroupIcon className="w-5 h-5 text-blue-600" />
                          <h4 className="font-medium text-blue-800">Active Users</h4>
                        </div>
                        <div className="text-2xl font-bold text-blue-600">
                          {realTimeData.userEngagement.uniqueUsers}
                        </div>
                        <p className="text-sm text-blue-700">Current session</p>
                      </div>

                      <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <TrophyIcon className="w-5 h-5 text-purple-600" />
                          <h4 className="font-medium text-purple-800">Conversion Rate</h4>
                        </div>
                        <div className="text-2xl font-bold text-purple-600">
                          {(realTimeData.business.conversionRate * 100).toFixed(1)}%
                        </div>
                        <p className="text-sm text-purple-700">Booking conversion</p>
                      </div>
                    </div>
                  )}

                  {/* Core Web Vitals Tab */}
                  {activeTab === 'vitals' && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(realTimeData.webVitals).map(([key, value]) => {
                        const thresholds = {
                          cls: { good: 0.1, poor: 0.25 },
                          fid: { good: 100, poor: 300 },
                          fcp: { good: 1800, poor: 3000 },
                          lcp: { good: 2500, poor: 4000 },
                          ttfb: { good: 800, poor: 1800 },
                          inp: { good: 200, poor: 500 }
                        };
                        
                        const threshold = thresholds[key as keyof typeof thresholds];
                        const status = getMetricStatus(value, threshold);
                        const StatusIcon = getStatusIcon(status);
                        
                        return (
                          <div key={key} className="bg-white border border-gray-200 rounded-lg p-4">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium text-gray-800">{key.toUpperCase()}</h4>
                              <StatusIcon className={`w-5 h-5 ${getStatusColor(status)}`} />
                            </div>
                            <div className="text-2xl font-bold text-gray-900 mb-1">
                              {key === 'cls' ? value.toFixed(3) : Math.round(value)}
                              {key !== 'cls' && key !== 'fid' && key !== 'inp' && 'ms'}
                            </div>
                            <p className={`text-sm ${getStatusColor(status)}`}>
                              {status.replace('-', ' ')}
                            </p>
                          </div>
                        );
                      })}
                    </div>
                  )}

                  {/* Resources Tab */}
                  {activeTab === 'resources' && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-white border border-gray-200 rounded-lg p-4">
                          <h4 className="font-medium text-gray-800 mb-2">Resource Loading</h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Total Requests</span>
                              <span className="font-medium">{realTimeData.resourceTiming.totalRequests}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Slow Requests</span>
                              <span className="font-medium text-red-600">{realTimeData.resourceTiming.slowRequests}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Avg Load Time</span>
                              <span className="font-medium">{Math.round(realTimeData.resourceTiming.avgLoadTime)}ms</span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-white border border-gray-200 rounded-lg p-4">
                          <h4 className="font-medium text-gray-800 mb-2">Data Transfer</h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Total Size</span>
                              <span className="font-medium">{Math.round(realTimeData.resourceTiming.totalSize / 1024)}KB</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Compression</span>
                              <span className="font-medium text-green-600">Gzip</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Cache Hit Rate</span>
                              <span className="font-medium text-green-600">87%</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Users Tab */}
                  {activeTab === 'users' && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-white border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <DevicePhoneMobileIcon className="w-5 h-5 text-blue-600" />
                            <h4 className="font-medium text-gray-800">Mobile</h4>
                          </div>
                          <div className="text-2xl font-bold text-blue-600">
                            {realTimeData.deviceMetrics.mobile}%
                          </div>
                        </div>

                        <div className="bg-white border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <ComputerDesktopIcon className="w-5 h-5 text-green-600" />
                            <h4 className="font-medium text-gray-800">Desktop</h4>
                          </div>
                          <div className="text-2xl font-bold text-green-600">
                            {realTimeData.deviceMetrics.desktop}%
                          </div>
                        </div>

                        <div className="bg-white border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <DeviceTabletIcon className="w-5 h-5 text-purple-600" />
                            <h4 className="font-medium text-gray-800">Tablet</h4>
                          </div>
                          <div className="text-2xl font-bold text-purple-600">
                            {realTimeData.deviceMetrics.tablet}%
                          </div>
                        </div>
                      </div>

                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <h4 className="font-medium text-gray-800 mb-4">User Engagement</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Session Duration</span>
                              <span className="font-medium">{Math.round(realTimeData.userEngagement.avgSessionDuration / 1000)}s</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Bounce Rate</span>
                              <span className="font-medium">{(realTimeData.userEngagement.bounceRate * 100).toFixed(1)}%</span>
                            </div>
                          </div>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Page Views</span>
                              <span className="font-medium">{realTimeData.userEngagement.pageViews}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Interactions</span>
                              <span className="font-medium">{realTimeData.userEngagement.interactions}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Errors Tab */}
                  {activeTab === 'errors' && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-white border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <BugAntIcon className="w-5 h-5 text-red-600" />
                            <h4 className="font-medium text-gray-800">JS Errors</h4>
                          </div>
                          <div className="text-2xl font-bold text-red-600">
                            {realTimeData.errors.jsErrors}
                          </div>
                        </div>

                        <div className="bg-white border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <WifiIcon className="w-5 h-5 text-yellow-600" />
                            <h4 className="font-medium text-gray-800">Network Errors</h4>
                          </div>
                          <div className="text-2xl font-bold text-yellow-600">
                            {realTimeData.errors.networkErrors}
                          </div>
                        </div>

                        <div className="bg-white border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <ExclamationTriangleIcon className="w-5 h-5 text-red-800" />
                            <h4 className="font-medium text-gray-800">Critical Errors</h4>
                          </div>
                          <div className="text-2xl font-bold text-red-800">
                            {realTimeData.errors.criticalErrors}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Business Tab */}
                  {activeTab === 'business' && (
                    <div className="space-y-4">
                      <div className="bg-white border border-gray-200 rounded-lg p-4">
                        <h4 className="font-medium text-gray-800 mb-4">Booking Funnel</h4>
                        <div className="space-y-3">
                          {realTimeData.business.bookingFunnelSteps.map((step, index) => (
                            <div key={index} className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-enterprise-brown text-white rounded-full flex items-center justify-center text-sm font-medium">
                                {index + 1}
                              </div>
                              <div className="flex-1">
                                <div className="flex items-center justify-between mb-1">
                                  <span className="text-sm text-gray-600">
                                    {['Landing', 'Retreats', 'Details', 'Booking', 'Payment', 'Confirmation'][index]}
                                  </span>
                                  <span className="font-medium">{step}%</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div 
                                    className="bg-enterprise-brown h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${step}%` }}
                                  />
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-white border border-gray-200 rounded-lg p-4">
                          <h4 className="font-medium text-gray-800 mb-2">Revenue Impact</h4>
                          <div className="text-2xl font-bold text-green-600">
                            ${Math.round(realTimeData.business.revenueImpact)}
                          </div>
                          <p className="text-sm text-gray-600">Performance-driven revenue</p>
                        </div>

                        <div className="bg-white border border-gray-200 rounded-lg p-4">
                          <h4 className="font-medium text-gray-800 mb-2">Conversion Rate</h4>
                          <div className="text-2xl font-bold text-enterprise-brown">
                            {(realTimeData.business.conversionRate * 100).toFixed(2)}%
                          </div>
                          <p className="text-sm text-gray-600">Booking conversion</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center h-32">
                  <div className="text-center">
                    <div className="w-8 h-8 border-2 border-enterprise-brown border-t-transparent rounded-full animate-spin mx-auto mb-2" />
                    <p className="text-gray-600">Loading performance data...</p>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default EnterprisePerformanceDashboard;

// Extend window interface for TypeScript
declare global {
  interface Window {
    webVitalsData?: any;
  }
}