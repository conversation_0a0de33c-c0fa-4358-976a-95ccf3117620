/**
 * 💎 BAKASANA DETAILS - Detale, które robią różnicę
 * Spójność kolorystyczna + Mikrointerakcje + Finalne detale
 */

/* =============================================
   🖼️ ZDJĘCIA - S<PERSON><PERSON>jna O<PERSON>r<PERSON>ka
   ============================================= */

/* Jednolity filtr dla wszystkich zdjęć */
.image-bakasana {
  filter: 
    contrast(0.95)
    brightness(1.05)
    saturate(0.9);
  transition: filter 0.6s ease;
  border-radius: 0; /* BAKASANA RULE: Zero border-radius */
}

/* Cieplejszy filtr na hover */
.image-bakasana:hover {
  filter: 
    contrast(1)
    brightness(1.08)
    saturate(0.95)
    sepia(0.05);
}

/* Dla czarno-białych zdj<PERSON> */
.image-monochrome {
  filter: 
    grayscale(1)
    contrast(1.1)
    brightness(0.95);
  transition: filter 0.6s ease;
}

.image-monochrome:hover {
  filter: 
    grayscale(0.8)
    contrast(1.05)
    brightness(1)
    sepia(0.1);
}

/* Obrazy hero z subtelnym parallax */
.image-hero {
  filter: 
    contrast(0.92)
    brightness(1.08)
    saturate(0.85);
  transition: all 0.8s cubic-bezier(0.25, 0.1, 0.25, 1);
}

/* =============================================
   ✨ DETALE, KTÓRE ROBIĄ RÓŻNICĘ
   ============================================= */

/* 1. Numeracja sekcji */
.section-number {
  font-family: 'Cormorant Garamond', serif;
  font-size: 8rem;
  font-weight: 300;
  color: rgba(139, 115, 85, 0.1);
  position: absolute;
  top: -40px;
  right: 10%;
  z-index: 0;
  line-height: 1;
  pointer-events: none;
  user-select: none;
}

@media (max-width: 1024px) {
  .section-number {
    font-size: 6rem;
    top: -30px;
    right: 5%;
  }
}

@media (max-width: 768px) {
  .section-number {
    font-size: 4rem;
    top: -20px;
    right: 5%;
    opacity: 0.5;
  }
}

/* 2. Cytat w hero */
.hero-quote {
  font-family: 'Cormorant Garamond', serif;
  font-size: clamp(1.25rem, 2.5vw, 1.75rem);
  font-weight: 300;
  font-style: italic;
  color: var(--accent-earth);
  text-align: center;
  margin: 3rem 0;
  position: relative;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-quote p {
  margin-bottom: 1rem;
  line-height: 1.4;
}

.hero-quote cite {
  font-size: 0.9em;
  opacity: 0.7;
  font-style: normal;
  letter-spacing: 0.05em;
  text-transform: uppercase;
}

.hero-quote::before,
.hero-quote::after {
  content: '"';
  font-size: 2em;
  color: var(--accent-warm);
  opacity: 0.3;
  position: absolute;
  font-family: 'Cormorant Garamond', serif;
}

.hero-quote::before {
  top: -10px;
  left: -20px;
}

.hero-quote::after {
  bottom: -30px;
  right: -20px;
}

/* 3. Delikatne linie przewodnie */
.section-divider {
  width: 60px;
  height: 1px;
  background: var(--accent-warm);
  margin: 4rem auto;
  position: relative;
}

.section-divider::after {
  content: '◈';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--accent-warm);
  background: var(--light-bg);
  padding: 0 1rem;
  font-size: 0.8rem;
}

/* Warianty divider */
.section-divider--lotus::after {
  content: '🪷';
  font-size: 1.2rem;
}

.section-divider--om::after {
  content: 'ॐ';
  font-size: 1.1rem;
}

.section-divider--minimal::after {
  content: '•';
  font-size: 1.5rem;
}

/* =============================================
   🎭 MIKROINTERAKCJE
   ============================================= */

/* Przyciski z osobowością */
.btn-bakasana {
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
  border: 1px solid var(--enterprise-brown);
  background: transparent;
  color: var(--enterprise-brown);
  padding: 1rem 2rem;
  font-family: 'Inter', sans-serif;
  font-weight: 300;
  font-size: 0.9rem;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  cursor: pointer;
  border-radius: 0; /* BAKASANA RULE */
}

.btn-bakasana::before {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--enterprise-brown);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
  z-index: -1;
}

.btn-bakasana:hover {
  color: var(--sanctuary);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(139, 115, 85, 0.2);
}

.btn-bakasana:hover::before {
  transform: scaleX(1);
  transform-origin: left;
}

/* Warianty przycisków */
.btn-bakasana--warm {
  border-color: var(--terra);
  color: var(--terra);
}

.btn-bakasana--warm::before {
  background: var(--terra);
}

.btn-bakasana--sage {
  border-color: var(--sage);
  color: var(--sage);
}

.btn-bakasana--sage::before {
  background: var(--sage);
}

/* Karty z subtelną animacją */
.card-bakasana {
  background: var(--sanctuary);
  border: 1px solid rgba(139, 115, 85, 0.1);
  transition: all 0.5s cubic-bezier(0.25, 0.1, 0.25, 1);
  position: relative;
  overflow: hidden;
  border-radius: 0; /* BAKASANA RULE */
}

.card-bakasana::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--terra),
    transparent
  );
  transition: left 0.6s ease;
}

.card-bakasana:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(139, 115, 85, 0.15);
  border-color: rgba(139, 115, 85, 0.2);
}

.card-bakasana:hover::before {
  left: 100%;
}

/* Linki z elegancką animacją */
.link-bakasana {
  color: var(--enterprise-brown);
  text-decoration: none;
  position: relative;
  font-weight: 400;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.link-bakasana::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--enterprise-brown);
  transition: width 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.link-bakasana:hover {
  color: var(--terra);
  transform: translateY(-1px);
}

.link-bakasana:hover::after {
  width: 100%;
  background: var(--terra);
}

/* =============================================
   💎 FINALNE DETALE
   ============================================= */

/* Kursor custom */
body {
  cursor: url('/cursor-lotus.svg') 12 12, auto;
}

/* Kursor na linkach i przyciskach */
a, button, .btn-bakasana, .link-bakasana {
  cursor: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='%238B7355' d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E") 12 12, pointer;
}

/* Selection color */
::selection {
  background: rgba(212, 165, 116, 0.2);
  color: var(--light-text);
}

::-moz-selection {
  background: rgba(212, 165, 116, 0.2);
  color: var(--light-text);
}

/* Smooth scroll z offsetem dla navbar */
html {
  scroll-behavior: smooth;
  scroll-padding-top: 100px;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--light-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-earth);
  border-radius: 0; /* BAKASANA RULE */
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-warm);
}

/* =============================================
   🏛️ ZASADY KOMPOZYCJI
   ============================================= */

/* Złoty podział - utility classes */
.golden-ratio-major {
  flex: 1.618;
}

.golden-ratio-minor {
  flex: 1;
}

.golden-ratio-left {
  width: 61.8%;
}

.golden-ratio-right {
  width: 38.2%;
}

/* Golden ratio grid */
.golden-grid {
  display: grid;
  grid-template-columns: 1.618fr 1fr;
  gap: 2rem;
  align-items: center;
}

.golden-grid--reverse {
  grid-template-columns: 1fr 1.618fr;
}

/* Golden ratio aspect ratios */
.aspect-golden {
  aspect-ratio: 1.618 / 1;
}

.aspect-golden-reverse {
  aspect-ratio: 1 / 1.618;
}

/* Asymetria kontrolowana */
.asymmetric-layout {
  display: grid;
  grid-template-columns: 1fr 1.618fr;
  gap: 3rem;
  align-items: center;
}

.asymmetric-layout--reverse {
  grid-template-columns: 1.618fr 1fr;
}

@media (max-width: 768px) {
  .asymmetric-layout,
  .asymmetric-layout--reverse {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .golden-grid,
  .golden-grid--reverse {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

/* Rytm wizualny - spacing system */
.rhythm-section {
  padding: 80px 0;
  margin-bottom: 0;
}

.rhythm-section:nth-child(3n) {
  margin-bottom: 0; /* Consistent spacing for all sections */
}

@media (max-width: 768px) {
  .rhythm-section {
    padding: 60px 0;
    margin-bottom: 0;
  }

  .rhythm-section:nth-child(3n) {
    margin-bottom: 0;
  }
}

/* Negatywna przestrzeń - minimum 30% */
.negative-space {
  padding: 0 15%;
}

@media (max-width: 1024px) {
  .negative-space {
    padding: 0 10%;
  }
}

@media (max-width: 768px) {
  .negative-space {
    padding: 0 5%;
  }
}

/* =============================================
   🌟 SPECJALNE EFEKTY
   ============================================= */

/* Subtelny parallax dla elementów */
.parallax-subtle {
  transform: translateZ(0);
  will-change: transform;
}

/* Breathing animation dla hero */
.breathing-element {
  animation: breathingGlow 4s ease-in-out infinite;
}

@keyframes breathingGlow {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

/* Floating elements */
.floating-element {
  animation: gentleFloat 6s ease-in-out infinite;
}

@keyframes gentleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

/* Staggered animations */
.stagger-animation {
  animation: fadeInUp 0.8s ease-out both;
}

.stagger-animation:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* =============================================
   ACCESSIBILITY & PERFORMANCE
   ============================================= */

@media (prefers-reduced-motion: reduce) {
  .breathing-element,
  .floating-element,
  .stagger-animation {
    animation: none !important;
  }
  
  .parallax-subtle {
    will-change: auto;
  }
  
  html {
    scroll-behavior: auto;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .section-number {
    color: rgba(0, 0, 0, 0.3);
  }
  
  .section-divider {
    background: #000;
  }
  
  .section-divider::after {
    color: #000;
    background: #fff;
  }
  
  .hero-quote::before,
  .hero-quote::after {
    color: #000;
  }
}

/* Focus styles for accessibility */
.btn-bakasana:focus-visible,
.link-bakasana:focus-visible {
  outline: 2px solid var(--accent-warm);
  outline-offset: 4px;
}

/* Print styles */
@media print {
  .floating-element,
  .section-number,
  .breathing-element {
    display: none;
  }
  
  .btn-bakasana,
  .link-bakasana {
    color: #000 !important;
    text-decoration: underline;
  }
}