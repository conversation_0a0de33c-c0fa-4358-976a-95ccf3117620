/* =============================================
   📝 BAKASANA - RESPONSIVE TYPOGRAPHY SYSTEM
   Fluid typography with perfect readability
   ============================================= */

/* ===== FLUID TYPOGRAPHY VARIABLES ===== */
:root {
  /* Base font size - adjustable via accessibility */
  --base-font-size: 16px;
  
  /* Typography scale - Perfect fourth (1.333) */
  --type-scale: 1.333;
  
  /* Line height system */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.7;
  
  /* Letter spacing */
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
  
  /* Font weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Fluid font sizes using clamp() */
  --text-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --text-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --text-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --text-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --text-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
  --text-2xl: clamp(1.5rem, 1.3rem + 1vw, 1.875rem);
  --text-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem);
  --text-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
  --text-5xl: clamp(3rem, 2.5rem + 2.5vw, 4rem);
  --text-6xl: clamp(4rem, 3rem + 5vw, 6rem);
  --text-7xl: clamp(6rem, 4rem + 10vw, 8rem);
}

/* ===== ACCESSIBILITY FONT SIZE ADJUSTMENTS ===== */
html[data-font-size="12"] {
  --base-font-size: 12px;
}

html[data-font-size="14"] {
  --base-font-size: 14px;
}

html[data-font-size="16"] {
  --base-font-size: 16px;
}

html[data-font-size="18"] {
  --base-font-size: 18px;
}

html[data-font-size="20"] {
  --base-font-size: 20px;
}

html[data-font-size="24"] {
  --base-font-size: 24px;
}

/* ===== HIGH CONTRAST TYPOGRAPHY ===== */
html[data-high-contrast="true"] {
  --text-color: #000000;
  --text-color-light: #333333;
  --text-color-muted: #666666;
}

html[data-high-contrast="true"] * {
  font-weight: var(--font-weight-medium) !important;
  text-shadow: none !important;
}

/* ===== BASE TYPOGRAPHY STYLES ===== */
html {
  font-size: var(--base-font-size);
  line-height: var(--line-height-normal);
  font-family: var(--font-inter), system-ui, -apple-system, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-size: var(--text-base);
  line-height: var(--line-height-normal);
  color: var(--charcoal);
  font-weight: var(--font-weight-normal);
  letter-spacing: var(--letter-spacing-normal);
}

/* ===== HEADING STYLES ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-cormorant), Georgia, serif;
  font-weight: var(--font-weight-light);
  line-height: var(--line-height-tight);
  color: var(--charcoal);
  margin-bottom: 0.5em;
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--text-6xl);
  line-height: 0.9;
  letter-spacing: var(--letter-spacing-tight);
  margin-bottom: 0.3em;
}

h2 {
  font-size: var(--text-5xl);
  line-height: 1;
  margin-bottom: 0.4em;
}

h3 {
  font-size: var(--text-4xl);
  line-height: 1.1;
  margin-bottom: 0.5em;
}

h4 {
  font-size: var(--text-3xl);
  line-height: 1.2;
  margin-bottom: 0.6em;
}

h5 {
  font-size: var(--text-2xl);
  line-height: 1.3;
  margin-bottom: 0.7em;
}

h6 {
  font-size: var(--text-xl);
  line-height: 1.4;
  margin-bottom: 0.8em;
}

/* ===== PARAGRAPH STYLES ===== */
p {
  font-size: var(--text-base);
  line-height: var(--line-height-relaxed);
  color: var(--charcoal);
  margin-bottom: 1.5em;
  max-width: 65ch; /* Optimal reading width */
}

p.lead {
  font-size: var(--text-lg);
  line-height: var(--line-height-relaxed);
  color: var(--stone);
  margin-bottom: 2em;
}

p.small {
  font-size: var(--text-sm);
  line-height: var(--line-height-normal);
  color: var(--stone);
}

/* ===== LINK STYLES ===== */
a {
  color: var(--temple-gold);
  text-decoration: none;
  transition: color 0.3s ease;
  position: relative;
}

a:hover {
  color: var(--golden-amber);
}

a:focus {
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
  border-radius: 2px;
}

/* ===== LIST STYLES ===== */
ul, ol {
  margin-bottom: 1.5em;
  padding-left: 1.5em;
}

li {
  font-size: var(--text-base);
  line-height: var(--line-height-normal);
  margin-bottom: 0.5em;
}

/* ===== BLOCKQUOTE STYLES ===== */
blockquote {
  font-family: var(--font-cormorant), Georgia, serif;
  font-size: var(--text-xl);
  font-style: italic;
  line-height: var(--line-height-relaxed);
  color: var(--stone);
  margin: 2em 0;
  padding: 1.5em;
  border-left: 4px solid var(--temple-gold);
  background: var(--whisper);
  position: relative;
}

blockquote::before {
  content: '"';
  font-size: 3em;
  color: var(--temple-gold);
  position: absolute;
  top: -0.5em;
  left: 0.5em;
  line-height: 1;
}

/* ===== CODE STYLES ===== */
code, pre {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--text-sm);
  background: var(--rice);
  border-radius: 4px;
}

code {
  padding: 0.2em 0.4em;
  color: var(--charcoal);
}

pre {
  padding: 1em;
  overflow-x: auto;
  margin: 1.5em 0;
  border: 1px solid var(--stone-light);
}

/* ===== UTILITY CLASSES ===== */
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }
.text-5xl { font-size: var(--text-5xl); }
.text-6xl { font-size: var(--text-6xl); }
.text-7xl { font-size: var(--text-7xl); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.leading-tight { line-height: var(--line-height-tight); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }

.tracking-tight { letter-spacing: var(--letter-spacing-tight); }
.tracking-normal { letter-spacing: var(--letter-spacing-normal); }
.tracking-wide { letter-spacing: var(--letter-spacing-wide); }
.tracking-wider { letter-spacing: var(--letter-spacing-wider); }
.tracking-widest { letter-spacing: var(--letter-spacing-widest); }

.font-serif { font-family: var(--font-cormorant), Georgia, serif; }
.font-sans { font-family: var(--font-inter), system-ui, sans-serif; }

/* ===== RESPONSIVE OVERRIDES ===== */
@media (max-width: 768px) {
  :root {
    --text-6xl: clamp(3rem, 8vw, 4rem);
    --text-5xl: clamp(2.5rem, 6vw, 3rem);
    --text-4xl: clamp(2rem, 5vw, 2.5rem);
  }
  
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.1;
  }
  
  p {
    line-height: 1.6;
  }
  
  blockquote {
    margin: 1.5em 0;
    padding: 1em;
    font-size: var(--text-lg);
  }
}

@media (max-width: 480px) {
  :root {
    --text-6xl: clamp(2.5rem, 10vw, 3rem);
    --text-5xl: clamp(2rem, 8vw, 2.5rem);
    --text-4xl: clamp(1.5rem, 6vw, 2rem);
  }
  
  p {
    max-width: none;
  }
  
  ul, ol {
    padding-left: 1em;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.5;
  }
  
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
    color: black !important;
  }
  
  p, blockquote {
    orphans: 3;
    widows: 3;
  }
  
  blockquote {
    border-left: 4px solid black;
    page-break-inside: avoid;
  }
  
  a {
    color: black !important;
    text-decoration: underline;
  }
  
  a[href^="http"]:after {
    content: " (" attr(href) ")";
    font-size: 0.8em;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus visible for keyboard navigation */
html[data-keyboard-navigation="true"] *:focus {
  outline: 2px solid var(--temple-gold) !important;
  outline-offset: 2px !important;
  border-radius: 2px !important;
}

/* Screen reader only text */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--charcoal);
  color: var(--sanctuary);
  padding: 8px 16px;
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  border-radius: 0;
  z-index: 1000;
  transition: top 0.3s ease;
}

.skip-link:focus {
  top: 6px;
  outline: 2px solid var(--temple-gold);
  outline-offset: 2px;
}

/* =============================================
   🌟 ENHANCED TYPOGRAPHY - Hierarchia z Charakterem
   ============================================= */

/* Nagłówek hero z wagą */
.hero-title-enhanced {
  font-family: 'Cormorant Garamond', serif;
  font-weight: 300;
  font-size: clamp(3.5rem, 8vw, 7rem);
  line-height: 0.9;
  letter-spacing: -0.02em;
  color: var(--charcoal);
  
  /* Subtelny gradient tekstowy */
  background: linear-gradient(
    135deg,
    var(--charcoal) 0%,
    var(--enterprise-brown) 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  
  /* Fallback dla starszych przeglądarek */
  @supports not (-webkit-background-clip: text) {
    color: var(--charcoal);
    background: none;
    -webkit-text-fill-color: initial;
  }
}

/* Podtytuł z elegancją */
.hero-subtitle-enhanced {
  font-family: 'Inter', sans-serif;
  font-weight: 300;
  font-size: clamp(1.1rem, 2vw, 1.4rem);
  letter-spacing: 0.08em;
  text-transform: uppercase;
  color: var(--enterprise-brown);
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* Nagłówki sekcji z charakterem */
.section-title-enhanced {
  font-family: 'Cormorant Garamond', serif;
  font-weight: 300;
  font-size: clamp(2.5rem, 5vw, 4rem);
  line-height: 1.1;
  letter-spacing: -0.01em;
  color: var(--charcoal);
  margin-bottom: 1.5rem;
  
  /* Subtelny efekt świetlny */
  text-shadow: 0 1px 3px rgba(139, 115, 85, 0.1);
}

/* Podtytuły z elegancją */
.section-subtitle-enhanced {
  font-family: 'Inter', sans-serif;
  font-weight: 300;
  font-size: clamp(1rem, 1.5vw, 1.2rem);
  letter-spacing: 0.05em;
  text-transform: uppercase;
  color: var(--enterprise-brown);
  opacity: 0.8;
  margin-bottom: 3rem;
}

/* Tekst body z lepszą czytelnością */
.body-text-enhanced {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: clamp(1rem, 1.2vw, 1.125rem);
  line-height: 1.7;
  color: var(--charcoal-light);
  margin-bottom: 1.5rem;
}

/* Cytaty z charakterem */
.quote-enhanced {
  font-family: 'Cormorant Garamond', serif;
  font-weight: 300;
  font-size: clamp(1.25rem, 2vw, 1.5rem);
  line-height: 1.4;
  font-style: italic;
  color: var(--enterprise-brown);
  text-align: center;
  margin: 2rem 0;
  position: relative;
}

.quote-enhanced::before,
.quote-enhanced::after {
  content: '"';
  font-size: 1.5em;
  color: var(--enterprise-brown);
  opacity: 0.5;
}

/* Linki z charakterem */
.link-enhanced {
  color: var(--enterprise-brown);
  text-decoration: none;
  font-weight: 400;
  position: relative;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.link-enhanced::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--enterprise-brown);
  transition: width 0.3s ease;
}

.link-enhanced:hover {
  color: var(--terra);
  transform: translateY(-1px);
}

.link-enhanced:hover::after {
  width: 100%;
}

/* Listy z charakterem */
.list-enhanced {
  list-style: none;
  padding-left: 0;
}

.list-enhanced li {
  position: relative;
  padding-left: 2rem;
  margin-bottom: 0.75rem;
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  color: var(--charcoal-light);
}

.list-enhanced li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--enterprise-brown);
  font-weight: bold;
  font-size: 1.2em;
}

/* Przyciski z charakterem */
.button-enhanced {
  font-family: 'Inter', sans-serif;
  font-weight: 300;
  font-size: clamp(0.875rem, 1vw, 1rem);
  letter-spacing: 0.05em;
  text-transform: uppercase;
  padding: 1rem 2rem;
  border: 1px solid var(--enterprise-brown);
  background: transparent;
  color: var(--enterprise-brown);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.1, 0.25, 1);
  position: relative;
  overflow: hidden;
}

.button-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--enterprise-brown);
  transition: left 0.4s ease;
  z-index: -1;
}

.button-enhanced:hover {
  color: var(--sanctuary);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(139, 115, 85, 0.2);
}

.button-enhanced:hover::before {
  left: 0;
}

/* Responsywne dostosowania */
@media (max-width: 768px) {
  .hero-title-enhanced {
    font-size: clamp(2.5rem, 10vw, 4rem);
    line-height: 1;
  }
  
  .hero-subtitle-enhanced {
    font-size: clamp(0.9rem, 3vw, 1.1rem);
    letter-spacing: 0.06em;
  }
  
  .section-title-enhanced {
    font-size: clamp(2rem, 7vw, 3rem);
  }
  
  .quote-enhanced {
    font-size: clamp(1.1rem, 3vw, 1.3rem);
    margin: 1.5rem 0;
  }
}

/* Tryb wysokiego kontrastu */
@media (prefers-contrast: high) {
  .hero-title-enhanced {
    background: none;
    -webkit-text-fill-color: initial;
    color: var(--charcoal);
    text-shadow: none;
  }
  
  .section-title-enhanced {
    text-shadow: none;
  }
  
  .link-enhanced::after {
    height: 2px;
  }
}