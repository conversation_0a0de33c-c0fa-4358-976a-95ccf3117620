'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useResponsiveStyles, getSectionStyles } from '../../components/OnlineClassesStyles';
import TransformationCTA from '../../components/TransformationCTA';



export default function OMniePage() {
  const { isDesktop, isTablet, isMobile } = useResponsiveStyles();
  const sectionStyles = getSectionStyles(isDesktop, isTablet, isMobile);

  return (
    <main className="bg-sanctuary min-h-screen">
      {/* HERO SECTION - Magazine Style Header */}
      <section className="magazine-hero">
        <div className="magazine-hero-content">
          <div className="magazine-header-line"></div>
          
          <h1 className="magazine-title">
            <PERSON>
          </h1>

          <p className="magazine-subtitle">
            Instruktorka jogi RYT 500 • Fizjoterapeutka
          </p>

          <div className="magazine-meta">
            Przewodniczka transformacyjnych podróży
          </div>
          
          <div className="magazine-header-line"></div>
        </div>
      </section>

      {/* SEKCJA GŁÓWNA - Dwukolumnowa */}
      <section style={sectionStyles.section}>
        <div style={sectionStyles.grid}>
          {/* Kolumna tekstowa (lewa) */}
          <div>
            <h2 style={sectionStyles.sectionTitle}>
              Moja droga
            </h2>

            <p style={{
              fontSize: '17px',
              fontFamily: 'Inter',
              fontWeight: 300,
              color: '#5B5754',
              lineHeight: '1.8',
              marginBottom: '32px'
            }}>
              Prawdziwa transformacja dzieje się poza matą — w codzienności, w sposób, w jaki 
              oddychamy, poruszamy się i odnosimy do świata.
            </p>

            <div style={{
              fontSize: '15px',
              fontFamily: 'Inter',
              fontWeight: 300,
              color: '#3D3A37',
              lineHeight: '1.9',
              letterSpacing: '0.02em',
              marginBottom: '56px',
              textAlign: 'left'
            }}>
              Bali i Sri Lanka nauczyły mnie słuchania tej prawdziwej ciszy, która mieszka 
              w sercu. Każda podróż to powrót do naszej autentycznej natury, do tego kim 
              naprawdę jesteśmy, gdy zdjemy wszystkie maski i społeczne oczekiwania.
            </div>

            <div style={{marginBottom: '64px'}}>
              <h3 style={{
                fontSize: '20px',
                fontFamily: 'Cormorant Garamond',
                fontWeight: 400,
                color: '#3D3A37',
                marginBottom: '28px'
              }}>
                Moje podejście
              </h3>
              
              <div>
                {[
                  'Łączę wiedzę medyczną z duchową praktyką',
                  'Tworzę bezpieczną przestrzeń eksploracji',
                  'Wspieram budowanie świadomej relacji z ciałem'
                ].map((approach, index) => (
                  <div key={index} style={{
                    padding: '16px 0',
                    paddingLeft: '24px',
                    borderLeft: '1px solid rgba(196, 165, 117, 0.1)',
                    position: 'relative'
                  }}>
                    <span style={{
                      fontSize: '14px',
                      fontFamily: 'Inter',
                      fontWeight: 300,
                      color: '#4A4744'
                    }}>
                      {approach}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Kolumna wizualna (prawa) */}
          <div style={sectionStyles.imageContainer}>
            <Image
              src="/images/profile/omnie-opt.webp"
              alt="Julia Jakubowicz - instruktorka jogi"
              fill
              style={{
                objectFit: 'cover',
                filter: 'grayscale(100%) brightness(1.05)'
              }}
              sizes={isMobile ? '100vw' : '45vw'}
              quality={95}
            />
          </div>
        </div>
      </section>

      {/* LINIA PODZIAŁU */}
      <div style={sectionStyles.divider}></div>

      {/* SEKCJA DOŚWIADCZENIE - Odwrócona dwukolumnowa */}
      <section style={{
        maxWidth: '1200px',
        margin: '0 auto',
        padding: isMobile ? '0 40px' : isTablet ? '0 60px' : '0 80px'
      }}>
        <div style={sectionStyles.gridReverse}>
          {/* Kolumna wizualna (lewa) */}
          <div style={sectionStyles.visualElement}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: isMobile ? '10px' : '20px'
            }}>
              <div style={{
                width: isMobile ? '40px' : '60px',
                height: isMobile ? '40px' : '60px',
                border: '1px solid rgba(196, 165, 117, 0.15)',
                borderRadius: '50%',
                transform: isMobile ? 'translateX(5px)' : 'translateX(10px)'
              }}></div>
              <div style={{
                width: isMobile ? '40px' : '60px',
                height: isMobile ? '40px' : '60px',
                border: '1px solid rgba(196, 165, 117, 0.15)',
                borderRadius: '50%',
                transform: isMobile ? 'translateX(-5px)' : 'translateX(-10px)'
              }}></div>
              <div style={{
                width: isMobile ? '40px' : '60px',
                height: isMobile ? '40px' : '60px',
                border: '1px solid rgba(196, 165, 117, 0.15)',
                borderRadius: '50%',
                transform: isMobile ? 'translateX(-15px)' : 'translateX(-30px)'
              }}></div>
            </div>
          </div>

          {/* Kolumna tekstowa (prawa) */}
          <div>
            <h2 style={sectionStyles.sectionTitle}>
              Doświadczenie
            </h2>

            <p style={{
              fontSize: '17px',
              fontFamily: 'Inter',
              fontWeight: 300,
              color: '#5B5754',
              lineHeight: '1.8',
              marginBottom: '32px'
            }}>
              Ponad 8 lat praktyki i nauczania jogi w różnych formach i miejscach.
            </p>

            <div style={{
              fontSize: '15px',
              fontFamily: 'Inter',
              fontWeight: 300,
              color: '#3D3A37',
              lineHeight: '1.9',
              letterSpacing: '0.02em',
              marginBottom: '56px',
              textAlign: 'left'
            }}>
              Od sceptycznej fizjoterapeutki do przewodniczki duchowych transformacji. 
              Każda podróż na Bali i Sri Lanka to lekcja pokory wobec starożytnej mądrości.
            </div>

            <div style={{marginBottom: '64px'}}>
              <h3 style={{
                fontSize: '20px',
                fontFamily: 'Cormorant Garamond',
                fontWeight: 400,
                color: '#3D3A37',
                marginBottom: '28px'
              }}>
                Kwalifikacje i certyfikaty
              </h3>
              
              <div>
                {[
                  { name: 'RYT 500 - Certified Yoga Teacher', duration: 'Yoga Alliance', note: 'Registered Yoga Alliance' },
                  { name: '8 lat praktyki nauczania', duration: 'Doświadczenie', note: 'Prowadzenie grup i sesji indywidualnych' },
                  { name: 'Fizjoterapeutka', duration: 'Terapia manualna', note: 'Medyczne podejście do jogi' },
                  { name: '200+ uczniów', duration: 'Azja', note: 'Retreaty na Bali i Sri Lanka' }
                ].map((option, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '20px 0',
                    borderBottom: index === 3 ? 'none' : '1px solid rgba(139, 133, 127, 0.15)'
                  }}>
                    <div>
                      <h4 style={{
                        fontSize: '15px',
                        fontFamily: 'Inter',
                        fontWeight: 400,
                        color: '#3D3A37',
                        margin: '0 0 4px 0'
                      }}>
                        {option.name}
                      </h4>
                      <p style={{
                        fontSize: '13px',
                        fontFamily: 'Inter',
                        fontWeight: 300,
                        color: '#8B857F',
                        margin: 0
                      }}>
                        {option.duration}
                      </p>
                    </div>
                    <span style={{
                      fontSize: '13px',
                      fontFamily: 'Inter',
                      fontWeight: 300,
                      color: '#8B857F'
                    }}>
                      {option.note}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* OSTATNIA SEKCJA - KONTAKT */}
      <TransformationCTA />
    </main>
  );
}