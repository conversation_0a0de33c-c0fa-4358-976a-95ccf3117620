'use client';

import React from 'react';

/**
 * 🏛️ GOLDEN RATIO COMPONENTS - Złoty Podział
 * Komponenty oparte na proporcjach złotego podziału (1:1.618)
 */

// Główny kontener złotego podziału
export const GoldenContainer = ({ 
  children, 
  reverse = false, 
  className = '',
  vertical = false,
  ...props 
}) => {
  const direction = vertical ? 'flex-col' : 'flex-row';
  const reverseClass = reverse ? 'flex-row-reverse' : '';
  
  return (
    <div 
      className={`flex ${direction} ${reverseClass} gap-8 items-center ${className}`}
      {...props}
    >
      {children}
    </div>
  );
};

// Sek<PERSON><PERSON> w<PERSON> (61.8%)
export const GoldenMajor = ({ 
  children, 
  className = '',
  ...props 
}) => (
  <div 
    className={`golden-ratio-major flex-[1.618] ${className}`}
    {...props}
  >
    {children}
  </div>
);

// Sek<PERSON><PERSON> m<PERSON> (38.2%)
export const GoldenMinor = ({ 
  children, 
  className = '',
  ...props 
}) => (
  <div 
    className={`golden-ratio-minor flex-[1] ${className}`}
    {...props}
  >
    {children}
  </div>
);

// Kompletny layout złotego podziału
export const GoldenLayout = ({ 
  major, 
  minor, 
  reverse = false,
  vertical = false,
  className = '',
  ...props 
}) => (
  <GoldenContainer 
    reverse={reverse} 
    vertical={vertical}
    className={className}
    {...props}
  >
    <GoldenMajor>
      {major}
    </GoldenMajor>
    <GoldenMinor>
      {minor}
    </GoldenMinor>
  </GoldenContainer>
);

// Hero z złotym podziałem
export const GoldenHero = ({ 
  title,
  subtitle,
  content,
  image,
  reverse = false,
  className = ''
}) => (
  <section className={`py-20 ${className}`}>
    <div className="container mx-auto px-4">
      <GoldenLayout
        reverse={reverse}
        major={
          <div className="space-y-6">
            {title && (
              <h1 className="hero-title-enhanced">
                {title}
              </h1>
            )}
            {subtitle && (
              <p className="hero-subtitle-enhanced">
                {subtitle}
              </p>
            )}
            {content && (
              <div className="body-text-enhanced">
                {content}
              </div>
            )}
          </div>
        }
        minor={
          <div className="relative">
            {image}
          </div>
        }
      />
    </div>
  </section>
);

// Sekcja z tekstem i obrazem w złotym podziale
export const GoldenSection = ({ 
  title,
  subtitle,
  content,
  image,
  reverse = false,
  className = '',
  number
}) => (
  <section className={`rhythm-section relative ${className}`}>
    {number && (
      <div className="section-number" aria-hidden="true">
        {number.toString().padStart(2, '0')}
      </div>
    )}
    
    <div className="container mx-auto px-4">
      <GoldenLayout
        reverse={reverse}
        major={
          <div className="space-y-6">
            {title && (
              <h2 className="section-title-enhanced">
                {title}
              </h2>
            )}
            {subtitle && (
              <p className="section-subtitle-enhanced">
                {subtitle}
              </p>
            )}
            {content && (
              <div className="body-text-enhanced">
                {content}
              </div>
            )}
          </div>
        }
        minor={
          <div className="relative">
            {image}
          </div>
        }
      />
    </div>
  </section>
);

// Grid oparty na złotym podziale
export const GoldenGrid = ({ 
  children, 
  className = '',
  ...props 
}) => {
  const childrenArray = React.Children.toArray(children);
  
  return (
    <div 
      className={`grid grid-cols-1 lg:grid-cols-[1.618fr_1fr] gap-8 items-start ${className}`}
      {...props}
    >
      {childrenArray.map((child, index) => (
        <div key={index} className={index === 0 ? 'lg:pr-4' : 'lg:pl-4'}>
          {child}
        </div>
      ))}
    </div>
  );
};

// Karta z proporcjami złotego podziału
export const GoldenCard = ({ 
  children, 
  className = '',
  aspectRatio = 'golden', // 'golden', 'square', 'wide'
  ...props 
}) => {
  const ratios = {
    golden: 'aspect-[1.618/1]',
    square: 'aspect-square',
    wide: 'aspect-[16/9]'
  };

  return (
    <div 
      className={`card-bakasana ${ratios[aspectRatio]} ${className}`}
      {...props}
    >
      {children}
    </div>
  );
};

// Spacer oparty na złotym podziale
export const GoldenSpacer = ({ 
  size = 'md',
  className = ''
}) => {
  const sizes = {
    xs: 'h-4',      // 16px
    sm: 'h-6',      // 24px  
    md: 'h-10',     // 40px (base * 1.618)
    lg: 'h-16',     // 64px (40 * 1.618)
    xl: 'h-26',     // 104px (64 * 1.618)
    '2xl': 'h-42'   // 168px (104 * 1.618)
  };

  return (
    <div 
      className={`${sizes[size]} ${className}`}
      aria-hidden="true"
    />
  );
};

// Responsive breakpoints dla złotego podziału
export const useGoldenBreakpoint = () => {
  const [breakpoint, setBreakpoint] = React.useState('desktop');

  React.useEffect(() => {
    const checkBreakpoint = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setBreakpoint('mobile');
      } else if (width < 1024) {
        setBreakpoint('tablet');
      } else {
        setBreakpoint('desktop');
      }
    };

    checkBreakpoint();
    window.addEventListener('resize', checkBreakpoint);
    return () => window.removeEventListener('resize', checkBreakpoint);
  }, []);

  return {
    breakpoint,
    isMobile: breakpoint === 'mobile',
    isTablet: breakpoint === 'tablet',
    isDesktop: breakpoint === 'desktop',
    shouldStack: breakpoint !== 'desktop'
  };
};

// Export wszystkich komponentów
const GoldenRatio = {
  GoldenContainer,
  GoldenMajor,
  GoldenMinor,
  GoldenLayout,
  GoldenHero,
  GoldenSection,
  GoldenGrid,
  GoldenCard,
  GoldenSpacer,
  useGoldenBreakpoint
};

export default GoldenRatio;