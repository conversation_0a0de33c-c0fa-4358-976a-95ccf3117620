'use client';

import React, { useState, useEffect } from 'react';

/**
 * 💎 BAKASANA DETAILS - Komponenty detali
 * Gotowe elementy do użycia w różnych sekcjach
 */

// Numeracja sekcji
export const SectionNumber = ({ number, className = '' }) => (
  <div className={`section-number ${className}`} aria-hidden="true">
    {number.toString().padStart(2, '0')}
  </div>
);

// Cytat w hero
export const HeroQuote = ({ quote, author, className = '' }) => (
  <blockquote className={`hero-quote ${className}`}>
    <p>"{quote}"</p>
    {author && <cite>— {author}</cite>}
  </blockquote>
);

// <PERSON>ie przewodnie
export const SectionDivider = ({ 
  variant = 'default', 
  className = '' 
}) => {
  const variants = {
    default: '',
    lotus: 'section-divider--lotus',
    om: 'section-divider--om',
    minimal: 'section-divider--minimal'
  };

  return (
    <div 
      className={`section-divider ${variants[variant]} ${className}`}
      role="separator"
      aria-hidden="true"
    />
  );
};

// Przycisk z mikrointerakcjami
export const BakasanaButton = ({ 
  children, 
  variant = 'earth', 
  onClick,
  href,
  className = '',
  ...props 
}) => {
  const variants = {
    earth: 'btn-bakasana',
    warm: 'btn-bakasana btn-bakasana--warm',
    sage: 'btn-bakasana btn-bakasana--sage'
  };

  const Component = href ? 'a' : 'button';
  const componentProps = href ? { href } : { onClick };

  return (
    <Component 
      className={`${variants[variant]} ${className}`}
      {...componentProps}
      {...props}
    >
      {children}
    </Component>
  );
};

// Karta z animacjami
export const BakasanaCard = ({ 
  children, 
  className = '',
  ...props 
}) => (
  <div className={`card-bakasana ${className}`} {...props}>
    {children}
  </div>
);

// Link z elegancką animacją
export const BakasanaLink = ({ 
  children, 
  href, 
  className = '',
  ...props 
}) => (
  <a 
    href={href} 
    className={`link-bakasana ${className}`}
    {...props}
  >
    {children}
  </a>
);

// Asymetryczny layout
export const AsymmetricLayout = ({ 
  children, 
  reverse = false, 
  className = '' 
}) => (
  <div className={`asymmetric-layout ${reverse ? 'asymmetric-layout--reverse' : ''} ${className}`}>
    {children}
  </div>
);

// Sekcja z rytmem wizualnym
export const RhythmSection = ({ 
  children, 
  className = '',
  ...props 
}) => (
  <section className={`rhythm-section ${className}`} {...props}>
    {children}
  </section>
);

// Negatywna przestrzeń
export const NegativeSpace = ({
  children,
  className = ''
}) => (
  <div className={`negative-space ${className}`}>
    {children}
  </div>
);

// Floating element
export const FloatingElement = ({ 
  className = '',
  size = 'md',
  delay = 0,
  ...props 
}) => {
  const sizes = {
    sm: 'w-12 h-12',
    md: 'w-20 h-20',
    lg: 'w-32 h-32'
  };

  return (
    <div 
      className={`floating-element ${sizes[size]} ${className}`}
      style={{ animationDelay: `${delay}s` }}
      aria-hidden="true"
      {...props}
    />
  );
};

// Breathing element
export const BreathingElement = ({ 
  children, 
  className = '',
  ...props 
}) => (
  <div className={`breathing-element ${className}`} {...props}>
    {children}
  </div>
);

// Staggered animation container
export const StaggerContainer = ({ 
  children, 
  className = '' 
}) => (
  <div className={className}>
    {React.Children.map(children, (child, index) => 
      React.cloneElement(child, {
        className: `${child.props.className || ''} stagger-animation`,
        style: {
          ...child.props.style,
          animationDelay: `${index * 0.1}s`
        }
      })
    )}
  </div>
);

// Parallax wrapper
export const ParallaxElement = ({ 
  children, 
  intensity = 0.3, 
  className = '' 
}) => {
  const [offset, setOffset] = React.useState(0);

  React.useEffect(() => {
    const handleScroll = () => {
      setOffset(window.pageYOffset * intensity);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [intensity]);

  return (
    <div 
      className={`parallax-subtle ${className}`}
      style={{ transform: `translateY(${offset}px)` }}
    >
      {children}
    </div>
  );
};

// Image z filtrem Bakasana
export const BakasanaImage = ({ 
  src, 
  alt, 
  variant = 'default',
  className = '',
  ...props 
}) => {
  const variants = {
    default: 'image-bakasana',
    monochrome: 'image-monochrome',
    hero: 'image-hero'
  };

  return (
    <img 
      src={src}
      alt={alt}
      className={`${variants[variant]} ${className}`}
      {...props}
    />
  );
};

// Gradient overlay
export const GradientOverlay = ({ 
  variant = 'light',
  className = '' 
}) => {
  const variants = {
    light: 'section-gradient-overlay',
    dark: 'section-gradient-overlay section-gradient-overlay--dark'
  };

  return (
    <div className={`${variants[variant]} ${className}`} aria-hidden="true" />
  );
};

// Transition między sekcjami
export const SectionTransition = ({
  type = 'light-to-dark',
  className = ''
}) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const types = {
    'light-to-dark': 'transition-light-to-dark',
    'dark-to-light': 'transition-dark-to-light',
    'wave': 'wave-transition',
    'wave-reverse': 'wave-transition wave-transition--reverse'
  };

  // Return consistent markup for SSR
  if (!mounted) {
    return (
      <div className={`section-transition ${className}`} aria-hidden="true" />
    );
  }

  return (
    <div className={`${types[type]} ${className}`} aria-hidden="true" />
  );
};

// Kompletny zestaw detali dla sekcji
export const SectionWithDetails = ({
  number,
  title,
  subtitle,
  children,
  dividerVariant = 'default',
  hasTransition = false,
  transitionType = 'light-to-dark',
  className = ''
}) => (
  <RhythmSection className={`relative ${className}`}>
    {number && <SectionNumber number={number} />}

    <div className="container">
      <NegativeSpace>
        {title && (
          <h2 className="section-title-enhanced text-center mb-4">
            {title}
          </h2>
        )}

        {subtitle && (
          <p className="section-subtitle-enhanced text-center">
            {subtitle}
          </p>
        )}

        <SectionDivider variant={dividerVariant} />

        {children}
      </NegativeSpace>
    </div>

    {hasTransition && (
      <SectionTransition type={transitionType} />
    )}
  </RhythmSection>
);

// Export wszystkich komponentów jako default
const BakasanaDetails = {
  SectionNumber,
  HeroQuote,
  SectionDivider,
  BakasanaButton,
  BakasanaCard,
  BakasanaLink,
  AsymmetricLayout,
  RhythmSection,
  NegativeSpace,
  FloatingElement,
  BreathingElement,
  StaggerContainer,
  ParallaxElement,
  BakasanaImage,
  GradientOverlay,
  SectionTransition,
  SectionWithDetails
};

export default BakasanaDetails;