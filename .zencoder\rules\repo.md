---
description: Repository Information Overview
alwaysApply: true
---

# Bakasana Travel Blog Information

## Summary
Elegant website for yoga retreats in Bali led by <PERSON> - a physiotherapist and certified yoga instructor (RYT 500). The site showcases transformational yoga retreats that combine yoga practice with exploring Bali's most beautiful locations.

## Structure
- **app/**: Next.js App Router with pages and routes
- **components/**: React components organized by functionality
- **data/**: Static data for blog posts, programs, and events
- **lib/**: Utility functions and Sanity integration
- **public/**: Static assets including images
- **sanity/**: Sanity CMS schemas and configuration
- **scripts/**: Utility scripts for image optimization and PWA
- **styles/**: CSS files for styling components

## Language & Runtime
**Language**: JavaScript/TypeScript (JSX/TSX)
**Version**: Next.js 15.3.2, React 18.3.1
**Build System**: Next.js with webpack
**Package Manager**: npm

## Dependencies
**Main Dependencies**:
- Next.js (15.3.2) - React framework with App Router
- React (18.3.1) - UI library
- Tailwind CSS (3.4.17) - Utility-first CSS framework
- Framer Motion (12.12.2) - Animation library
- Sanity - Headless CMS for content management
- Mapbox GL (3.13.0) - Interactive maps
- Sharp (0.34.1) - Image optimization

**Development Dependencies**:
- ESLint (9.27.0) - Code linting
- TypeScript (5.4.5) - Type checking
- Puppeteer (22.1.0) - Browser automation
- Next Bundle Analyzer - Bundle size analysis
- Cross-env - Environment variable management

## Build & Installation
```bash
# Install dependencies
npm install

# Development server
npm run dev

# Production build
npm run build

# Start production server
npm start

# Analyze bundle
npm run build:analyze
```

## Sanity CMS
**Configuration**: `sanity.config.js`
**Schemas**: Retreats, testimonials, blog posts, authors, FAQs
**Commands**:
```bash
# Run Sanity development server
npm run sanity:dev

# Build Sanity studio
npm run sanity:build

# Deploy Sanity studio
npm run sanity:deploy
```

## Performance Optimization
**Image Optimization**: Sharp for processing, next/image for delivery
**Bundle Optimization**: Code splitting, tree shaking, compression
**Caching Strategy**: Long-term caching with immutable assets
**PWA Support**: Service worker, manifest.json, screenshots generation

## SEO
**Meta Tags**: Dynamic metadata with Open Graph and Twitter Cards
**Structured Data**: JSON-LD for better search engine visibility
**Sitemap**: Automatically generated with next-sitemap
**Analytics**: Vercel Analytics and Speed Insights integration

## Deployment
**Platform**: Vercel (implied from dependencies)
**Environment**: Node.js production environment
**Build Command**: `npm run build`
**Start Command**: `npm start`
**Output**: Static and server-rendered pages