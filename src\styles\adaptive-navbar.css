/**
 * 🎯 ADAPTIVE NAVBAR - Elegancja <PERSON>
 * Navbar, który harmonizuje z każdą sekcją
 */

/* =============================================
   NAVBAR CHAMELEON BASE
   ============================================= */

.navbar-adaptive {
  background: transparent;
  transition: all 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);
  backdrop-filter: blur(0px);
  border-bottom: 1px solid transparent;
}

/* =============================================
   LIGHT MODE - Na jasnych sekcjach
   ============================================= */

.navbar-light-mode {
  color: var(--charcoal);
}

.navbar-light-mode .logo { 
  filter: invert(1); 
}

.navbar-light-mode a { 
  color: var(--charcoal); 
}

.navbar-light-mode .nav-link {
  color: var(--charcoal);
}

.navbar-light-mode .nav-link:hover {
  color: var(--enterprise-brown);
}

.navbar-light-mode button {
  color: var(--charcoal);
}

.navbar-light-mode button:hover {
  color: var(--enterprise-brown);
}

/* =============================================
   DARK MODE - Na ciemnych sekcjach lub po scrollu
   ============================================= */

.navbar-dark-mode,
.navbar-scrolled {
  background: rgba(44, 44, 44, 0.95);
  backdrop-filter: blur(20px);
  color: var(--sanctuary);
  box-shadow: 0 2px 30px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-dark-mode a,
.navbar-scrolled a {
  color: var(--sanctuary);
}

.navbar-dark-mode .nav-link,
.navbar-scrolled .nav-link {
  color: var(--sanctuary);
}

.navbar-dark-mode .nav-link:hover,
.navbar-scrolled .nav-link:hover {
  color: var(--golden-hour, #D4AF7A);
}

.navbar-dark-mode button,
.navbar-scrolled button {
  color: var(--sanctuary);
}

.navbar-dark-mode button:hover,
.navbar-scrolled button:hover {
  color: var(--golden-hour, #D4AF7A);
}

/* =============================================
   ACTIVE PAGE INDICATOR
   ============================================= */

.nav-link {
  position: relative;
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 2px;
  background: var(--golden-hour, #D4AF7A);
  border-radius: 2px;
  animation: slideIn 0.3s ease-out;
}

/* Light mode active indicator */
.navbar-light-mode .nav-link.active::after {
  background: var(--enterprise-brown);
}

/* Dark mode active indicator */
.navbar-dark-mode .nav-link.active::after,
.navbar-scrolled .nav-link.active::after {
  background: var(--golden-hour, #D4AF7A);
}

/* =============================================
   SMOOTH TRANSITIONS
   ============================================= */

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 24px;
    opacity: 1;
  }
}

/* Hover effects for nav links */
.nav-link:hover {
  transform: translateY(-1px);
}

.nav-link:hover::before {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 1px;
  background: currentColor;
  opacity: 0.5;
  animation: expandLine 0.3s ease-out forwards;
}

@keyframes expandLine {
  to {
    width: 100%;
  }
}

/* =============================================
   MOBILE MENU ENHANCEMENTS
   ============================================= */

.mobile-menu-backdrop {
  background: rgba(44, 44, 44, 0.8);
  backdrop-filter: blur(10px);
}

.mobile-menu-panel {
  background: rgba(253, 252, 248, 0.98);
  backdrop-filter: blur(30px);
  border-left: 1px solid rgba(139, 115, 85, 0.1);
}

/* Mobile menu animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mobile-menu-item {
  animation: fadeInUp 0.6s ease-out both;
}

/* =============================================
   RESPONSIVE ADAPTATIONS
   ============================================= */

@media (max-width: 1023px) {
  .navbar-adaptive {
    padding: 0 1rem;
  }
  
  .nav-link.active::after {
    bottom: -4px;
    width: 16px;
    height: 1px;
  }
}

@media (max-width: 767px) {
  .navbar-adaptive {
    padding: 0 0.75rem;
  }
  
  .navbar-adaptive .container {
    padding: 0;
  }
}

/* =============================================
   ACCESSIBILITY IMPROVEMENTS
   ============================================= */

@media (prefers-reduced-motion: reduce) {
  .navbar-adaptive,
  .nav-link,
  .nav-link::after,
  .nav-link::before {
    transition: none !important;
    animation: none !important;
  }
}

/* Focus styles */
.nav-link:focus-visible {
  outline: 2px solid var(--enterprise-brown);
  outline-offset: 4px;
  border-radius: 2px;
}

.navbar-dark-mode .nav-link:focus-visible,
.navbar-scrolled .nav-link:focus-visible {
  outline-color: var(--golden-hour, #D4AF7A);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .navbar-light-mode {
    background: rgba(255, 255, 255, 0.95);
    border-bottom: 2px solid #000;
  }
  
  .navbar-dark-mode,
  .navbar-scrolled {
    background: rgba(0, 0, 0, 0.95);
    border-bottom: 2px solid #fff;
  }
  
  .nav-link.active::after {
    height: 3px;
  }
}