'use client';

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * UnifiedCard - Ujednolicony system kart BAKASANA
 * Elegancja Old Money + Ciepły minimalizm + Organiczne elementy
 */

const cardVariants = {
  // DEFAULT - Standardowe karty
  default: {
    base: "bg-sanctuary border border-stone-light/20",
    hover: "hover:shadow-elegant hover:border-enterprise-brown/20 hover:-translate-y-1",
    focus: "focus-within:ring-2 focus-within:ring-enterprise-brown/10"
  },
  
  // ELEVATED - Karty z większym naciskiem
  elevated: {
    base: "bg-sanctuary shadow-elegant border border-enterprise-brown/10",
    hover: "hover:shadow-premium-shadow hover:border-enterprise-brown/30 hover:-translate-y-2",
    focus: "focus-within:ring-2 focus-within:ring-enterprise-brown/20"
  },
  
  // MINIMAL - Ultra-subtelne karty
  minimal: {
    base: "bg-transparent border-0",
    hover: "hover:bg-whisper/50",
    focus: "focus-within:bg-whisper/30"
  },
  
  // WARM - Ciepłe, organiczne karty
  warm: {
    base: "bg-linen border border-whisper",
    hover: "hover:bg-sanctuary hover:shadow-elegant hover:border-enterprise-brown/20",
    focus: "focus-within:ring-2 focus-within:ring-terra/20"
  }
};

const paddingVariants = {
  none: "p-0",
  sm: "p-4",
  md: "p-6", 
  lg: "p-8",
  xl: "p-12"
};

export default function UnifiedCard({
  children,
  variant = 'default',
  padding = 'md',
  className = '',
  ...props
}) {
  const variantStyles = cardVariants[variant];
  const paddingStyles = paddingVariants[padding];
  
  return (
    <div
      className={cn(
        // Base styles - Old Money elegance
        "transition-all duration-300 ease-out",
        "overflow-hidden", // Dla organicznych elementów
        
        // Variant styles
        variantStyles.base,
        variantStyles.hover,
        variantStyles.focus,
        
        // Padding
        paddingStyles,
        
        // Custom className
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

// Komponenty strukturalne karty

export function CardHeader({ children, className = '', ...props }) {
  return (
    <div 
      className={cn("mb-6", className)} 
      {...props}
    >
      {children}
    </div>
  );
}

export function CardTitle({ children, className = '', level = 3, ...props }) {
  const Tag = `h${level}`;
  
  return (
    <Tag 
      className={cn(
        "font-cormorant font-light text-charcoal leading-tight",
        level === 1 && "text-4xl mb-4",
        level === 2 && "text-3xl mb-3", 
        level === 3 && "text-2xl mb-3",
        level === 4 && "text-xl mb-2",
        className
      )} 
      {...props}
    >
      {children}
    </Tag>
  );
}

export function CardDescription({ children, className = '', ...props }) {
  return (
    <p 
      className={cn(
        "text-sage leading-relaxed font-inter font-light",
        className
      )} 
      {...props}
    >
      {children}
    </p>
  );
}

export function CardContent({ children, className = '', ...props }) {
  return (
    <div 
      className={cn("space-y-4", className)} 
      {...props}
    >
      {children}
    </div>
  );
}

export function CardFooter({ children, className = '', ...props }) {
  return (
    <div 
      className={cn(
        "mt-6 pt-6 border-t border-stone-light/20 flex items-center justify-between",
        className
      )} 
      {...props}
    >
      {children}
    </div>
  );
}

// Wyspecjalizowane warianty kart

export function RetreatCard({ children, ...props }) {
  return (
    <UnifiedCard variant="elevated" padding="lg" {...props}>
      {children}
    </UnifiedCard>
  );
}

export function TestimonialCard({ children, ...props }) {
  return (
    <UnifiedCard variant="warm" padding="lg" {...props}>
      {children}
    </UnifiedCard>
  );
}

export function ServiceCard({ children, ...props }) {
  return (
    <UnifiedCard variant="default" padding="md" {...props}>
      {children}
    </UnifiedCard>
  );
}

export function MinimalCard({ children, ...props }) {
  return (
    <UnifiedCard variant="minimal" padding="md" {...props}>
      {children}
    </UnifiedCard>
  );
}