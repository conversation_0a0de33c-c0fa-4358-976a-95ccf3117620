@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import core optimized styles */
@import url('../styles/core.css');
@import url('../styles/bakasana-details.css');
@import url('../styles/typography.css');
@import url('../styles/sections.css');
@import url('../styles/section-transitions.css');
@import url('../styles/microinteractions.css');
@import url('../styles/harmony-enhancements.css');

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
/* Critical CSS for above-the-fold content */
@layer base {
  html {
    scroll-behavior: smooth;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--sanctuary, #FDFCF8);
    color: var(--charcoal, #2A2724);
    margin: 0;
    padding: 0;
    font-display: swap;
    will-change: scroll-position;
  }

  .navigation {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 100;
    background: transparent;
  }

  .hero {
    height: 100vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: linear-gradient(135deg, #FDFCF8 0%, #F9F7F2 50%, #F5F3EF 100%);
  }

  /* Optimize images for better performance */
  img {
    content-visibility: auto;
    contain-intrinsic-size: 1px 1000px;
  }

  /* Optimize animations for 60fps */
  * {
    will-change: auto;
  }

  /* GPU acceleration for smooth scrolling */
  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }
}

/* ===== WHATSAPP BUTTON ===== */
.whatsapp-elegant {
  background-color: #8B7355;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.whatsapp-elegant:hover {
  background-color: rgba(139, 115, 85, 0.9);
  transform: scale(1.05);
}

.whatsapp-float {
  position: fixed;
  bottom: 2.5rem;
  right: 2.5rem;
  box-shadow: 0 4px 12px rgba(139, 115, 85, 0.15);
  z-index: 998;
}

@media (max-width: 768px) {
  .whatsapp-float {
    bottom: 1.5rem;
    right: 1.5rem;
  }
}

.whatsapp-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: white;
}