'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

/**
 * 🎨 THEME PROVIDER - Zarządzanie motywami
 * Zapewnia spójność kolorystyczną w całej aplikacji
 */

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState('light');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    
    // Check for saved theme preference or default to 'light'
    const savedTheme = localStorage.getItem('bakasana-theme') || 'light';
    setTheme(savedTheme);
    
    // Apply theme to document
    document.documentElement.setAttribute('data-theme', savedTheme);
  }, []);

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    localStorage.setItem('bakasana-theme', newTheme);
    document.documentElement.setAttribute('data-theme', newTheme);
  };

  const value = {
    theme,
    toggleTheme,
    isDark: theme === 'dark',
    isLight: theme === 'light'
  };

  // Prevent hydration mismatch
  if (!mounted) {
    return <div style={{ visibility: 'hidden' }}>{children}</div>;
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * 🎯 SECTION DETECTOR - Wykrywa typ sekcji
 * Pomaga navbarowi dostosować się do tła
 */
export const useSectionDetector = () => {
  const [currentSection, setCurrentSection] = useState('light');

  useEffect(() => {
    let ticking = false;
    let lastScrollY = 0;
    
    const detectSection = () => {
      const scrollPosition = window.scrollY;
      
      // Throttle - only check if scroll changed significantly
      if (Math.abs(scrollPosition - lastScrollY) < 50) {
        ticking = false;
        return;
      }
      
      lastScrollY = scrollPosition;
      
      // Simple scroll-based detection instead of elementFromPoint
      // Hero section is typically light, content sections vary
      if (scrollPosition < 200) {
        setCurrentSection('light');
      } else {
        // For now, assume content sections are light
        // This can be enhanced with specific scroll thresholds
        setCurrentSection('light');
      }
      
      ticking = false;
    };

    // Initial detection
    detectSection();
    
    // Throttled scroll handler
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(detectSection);
        ticking = true;
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return currentSection;
};

/**
 * 🌈 COLOR UTILITIES - Pomocnicze funkcje kolorów
 */
export const colorUtils = {
  // Get CSS variable value
  getCSSVariable: (variable) => {
    if (typeof window === 'undefined') return '';
    return getComputedStyle(document.documentElement)
      .getPropertyValue(variable)
      .trim();
  },

  // Set CSS variable
  setCSSVariable: (variable, value) => {
    if (typeof window === 'undefined') return;
    document.documentElement.style.setProperty(variable, value);
  },

  // Convert hex to rgba
  hexToRgba: (hex, alpha = 1) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
  },

  // Get theme colors
  getThemeColors: (theme = 'light') => ({
    background: theme === 'light' ? '#FDFCF8' : '#1A1A1A',
    surface: theme === 'light' ? '#F8F6F0' : '#242424',
    text: theme === 'light' ? '#2C2C2C' : '#F5F5F0',
    accent: '#D4A574',
    earth: '#8B7355',
    sage: '#9FA68D'
  })
};

/**
 * 🎨 THEME HOOK - Łatwy dostęp do kolorów motywu
 */
export const useThemeColors = () => {
  const { theme } = useTheme();
  return colorUtils.getThemeColors(theme);
};

/**
 * 📱 RESPONSIVE THEME - Dostosowanie do urządzenia
 */
export const useResponsiveTheme = () => {
  const [deviceType, setDeviceType] = useState('desktop');

  useEffect(() => {
    const detectDevice = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setDeviceType('mobile');
      } else if (width < 1024) {
        setDeviceType('tablet');
      } else {
        setDeviceType('desktop');
      }
    };

    detectDevice();
    window.addEventListener('resize', detectDevice);
    return () => window.removeEventListener('resize', detectDevice);
  }, []);

  return {
    deviceType,
    isMobile: deviceType === 'mobile',
    isTablet: deviceType === 'tablet',
    isDesktop: deviceType === 'desktop'
  };
};

export default ThemeProvider;