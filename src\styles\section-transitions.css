/**
 * 💫 SECTION TRANSITIONS - Płynn<PERSON>
 * Miękkie gradacje między jasnymi i ciemnymi sekcjami
 */

/* =============================================
   SECTION TRANSITION BASE
   ============================================= */

.section-transition {
  position: relative;
  margin-top: -100px;
  padding-top: 150px;
  z-index: 1;
}

.section-transition::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(
    to bottom,
    var(--sanctuary) 0%,
    transparent 100%
  );
  pointer-events: none;
  z-index: -1;
}

/* =============================================
   LIGHT TO DARK TRANSITIONS
   ============================================= */

/* Przejście z jasnej do ciemnej sekcji - IMPROVED */
.transition-light-to-dark {
  position: relative;
  /* Fallback for browsers without color-mix support */
  background: linear-gradient(
    180deg,
    var(--sanctuary) 0%,
    rgba(253, 252, 248, 0.9) 25%,
    rgba(249, 247, 243, 0.7) 60%,
    rgba(42, 39, 36, 0.9) 85%,
    var(--charcoal) 100%
  );
  /* Modern browsers with color-mix support */
  background: linear-gradient(
    180deg,
    var(--sanctuary) 0%,
    color-mix(in srgb, var(--sanctuary) 85%, var(--whisper) 15%) 25%,
    color-mix(in srgb, var(--whisper) 70%, var(--charcoal) 30%) 60%,
    color-mix(in srgb, var(--charcoal) 90%, var(--sanctuary) 10%) 85%,
    var(--charcoal) 100%
  );
  padding: 100px 0;
  margin: -50px 0;
  overflow: hidden;
  z-index: 2;
}

.transition-light-to-dark::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  /* Fallback */
  background:
    radial-gradient(ellipse at top, rgba(139, 115, 85, 0.08) 0%, transparent 70%),
    radial-gradient(ellipse at bottom, rgba(42, 39, 36, 0.12) 0%, transparent 70%);
  /* Modern browsers */
  background:
    radial-gradient(ellipse at top, color-mix(in srgb, var(--enterprise-brown) 8%, transparent 92%) 0%, transparent 70%),
    radial-gradient(ellipse at bottom, color-mix(in srgb, var(--charcoal) 12%, transparent 88%) 0%, transparent 70%);
  pointer-events: none;
  z-index: -1;
}

/* =============================================
   DARK TO LIGHT TRANSITIONS
   ============================================= */

/* Przejście z ciemnej do jasnej sekcji - IMPROVED */
.transition-dark-to-light {
  position: relative;
  /* Fallback for browsers without color-mix support */
  background: linear-gradient(
    180deg,
    var(--charcoal) 0%,
    rgba(42, 39, 36, 0.9) 25%,
    rgba(210, 205, 198, 0.7) 60%,
    rgba(253, 252, 248, 0.9) 85%,
    var(--sanctuary) 100%
  );
  /* Modern browsers with color-mix support */
  background: linear-gradient(
    180deg,
    var(--charcoal) 0%,
    color-mix(in srgb, var(--charcoal) 85%, var(--ash) 15%) 25%,
    color-mix(in srgb, var(--ash) 70%, var(--sanctuary) 30%) 60%,
    color-mix(in srgb, var(--sanctuary) 90%, var(--charcoal) 10%) 85%,
    var(--sanctuary) 100%
  );
  padding: 100px 0;
  margin: -50px 0;
  overflow: hidden;
  z-index: 2;
}

.transition-dark-to-light::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  /* Fallback */
  background:
    radial-gradient(ellipse at top, rgba(42, 39, 36, 0.12) 0%, transparent 70%),
    radial-gradient(ellipse at bottom, rgba(139, 115, 85, 0.08) 0%, transparent 70%);
  /* Modern browsers */
  background:
    radial-gradient(ellipse at top, color-mix(in srgb, var(--charcoal) 12%, transparent 88%) 0%, transparent 70%),
    radial-gradient(ellipse at bottom, color-mix(in srgb, var(--enterprise-brown) 8%, transparent 92%) 0%, transparent 70%);
  pointer-events: none;
  z-index: -1;
}

/* =============================================
   ORGANIC WAVE TRANSITIONS
   ============================================= */

/* Organiczne fale między sekcjami */
.wave-transition {
  position: relative;
  height: 150px;
  overflow: hidden;
}

.wave-transition::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: var(--sanctuary);
  clip-path: polygon(0 0, 100% 0, 100% 60%, 0 100%);
  z-index: 1;
}

.wave-transition::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: var(--charcoal);
  clip-path: polygon(0 40%, 100% 0, 100% 100%, 0 100%);
  z-index: 0;
}

/* Odwrócona fala */
.wave-transition--reverse::before {
  clip-path: polygon(0 40%, 100% 0, 100% 100%, 0 100%);
  background: var(--charcoal);
}

.wave-transition--reverse::after {
  clip-path: polygon(0 0, 100% 0, 100% 60%, 0 100%);
  background: var(--sanctuary);
}

/* =============================================
   GRADIENT OVERLAYS
   ============================================= */

/* Subtelne gradient overlay dla sekcji */
.section-gradient-overlay {
  position: relative;
}

.section-gradient-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(139, 115, 85, 0.02) 0%,
    transparent 50%,
    rgba(212, 165, 116, 0.02) 100%
  );
  pointer-events: none;
  z-index: 1;
}

/* Ciemny gradient overlay */
.section-gradient-overlay--dark::before {
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.1) 0%,
    transparent 50%,
    rgba(42, 39, 36, 0.05) 100%
  );
}

/* =============================================
   BREATHING SECTIONS
   ============================================= */

/* Oddychające sekcje z subtelną animacją */
.breathing-section {
  animation: breathingSection 8s ease-in-out infinite;
}

@keyframes breathingSection {
  0%, 100% {
    background-color: var(--sanctuary);
  }
  50% {
    background-color: rgba(253, 252, 248, 0.98);
  }
}

/* Ciemna wersja oddychającej sekcji */
.breathing-section--dark {
  animation: breathingSectionDark 8s ease-in-out infinite;
}

@keyframes breathingSectionDark {
  0%, 100% {
    background-color: var(--charcoal);
  }
  50% {
    background-color: rgba(42, 39, 36, 0.95);
  }
}

/* =============================================
   PARALLAX TRANSITIONS
   ============================================= */

/* Parallax efekt dla przejść */
.parallax-transition {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.parallax-transition-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 120%;
  background: linear-gradient(
    180deg,
    var(--sanctuary) 0%,
    rgba(253, 252, 248, 0.5) 50%,
    var(--charcoal) 100%
  );
  will-change: transform;
}

/* =============================================
   RESPONSIVE ADAPTATIONS
   ============================================= */

/* =============================================
   RESPONSIVE IMPROVEMENTS - ENHANCED
   ============================================= */

@media (max-width: 1024px) {
  .section-transition {
    margin-top: -75px;
    padding-top: 125px;
  }

  .section-transition::before {
    height: 150px;
  }

  .transition-light-to-dark,
  .transition-dark-to-light {
    padding: 75px 0;
    margin: -25px 0;
    /* Ensure proper stacking on tablets */
    isolation: isolate;
  }

  .wave-transition {
    height: 100px;
  }
}

@media (max-width: 768px) {
  .section-transition {
    margin-top: -50px;
    padding-top: 100px;
  }

  .section-transition::before {
    height: 100px;
  }

  .transition-light-to-dark,
  .transition-dark-to-light {
    padding: 50px 0;
    margin: -20px 0;
    /* Prevent overflow issues on mobile */
    min-height: 120px;
    isolation: isolate;
  }

  .transition-light-to-dark::before,
  .transition-dark-to-light::before {
    /* Reduce complexity on mobile for better performance */
    background:
      radial-gradient(ellipse at center, rgba(139, 115, 85, 0.06) 0%, transparent 60%);
  }

  .wave-transition {
    height: 75px;
  }

  .parallax-transition {
    height: 150px;
  }
}

@media (max-width: 480px) {
  .section-transition {
    margin-top: -20px;
    padding-top: 60px;
  }

  .section-transition::before {
    height: 60px;
  }

  .transition-light-to-dark,
  .transition-dark-to-light {
    padding: 40px 0;
    margin: -15px 0;
    min-height: 100px;
    /* Simplified gradients for small screens */
    background: linear-gradient(
      180deg,
      var(--sanctuary) 0%,
      var(--whisper) 50%,
      var(--charcoal) 100%
    );
  }

  .transition-dark-to-light {
    background: linear-gradient(
      180deg,
      var(--charcoal) 0%,
      var(--ash) 50%,
      var(--sanctuary) 100%
    );
  }

  .transition-light-to-dark::before,
  .transition-dark-to-light::before {
    /* Minimal overlay on very small screens */
    background: radial-gradient(ellipse at center, rgba(139, 115, 85, 0.04) 0%, transparent 50%);
  }

  .wave-transition {
    height: 50px;
  }

  .parallax-transition {
    height: 100px;
  }
}

/* =============================================
   ACCESSIBILITY & PERFORMANCE - ENHANCED
   ============================================= */

@media (prefers-reduced-motion: reduce) {
  .breathing-section,
  .breathing-section--dark {
    animation: none;
  }

  .parallax-transition-layer {
    will-change: auto;
  }

  .section-transition,
  .transition-light-to-dark,
  .transition-dark-to-light {
    transition: none;
    /* Simplified static gradients for reduced motion */
    background: var(--sanctuary);
  }

  .transition-dark-to-light {
    background: var(--charcoal);
  }

  .transition-light-to-dark::before,
  .transition-dark-to-light::before {
    display: none; /* Remove complex overlays */
  }
}

/* High contrast mode - ENHANCED */
@media (prefers-contrast: high) {
  .section-gradient-overlay::before,
  .section-gradient-overlay--dark::before {
    display: none;
  }

  .transition-light-to-dark {
    background: linear-gradient(
      180deg,
      #ffffff 0%,
      #e0e0e0 25%,
      #808080 50%,
      #404040 75%,
      #000000 100%
    ) !important;
  }

  .transition-dark-to-light {
    background: linear-gradient(
      180deg,
      #000000 0%,
      #404040 25%,
      #808080 50%,
      #e0e0e0 75%,
      #ffffff 100%
    ) !important;
  }

  .transition-light-to-dark::before,
  .transition-dark-to-light::before {
    display: none !important;
  }
}

/* Focus management for keyboard navigation */
.transition-light-to-dark:focus-within,
.transition-dark-to-light:focus-within {
  outline: 2px solid var(--enterprise-brown);
  outline-offset: 4px;
}

/* Screen reader support */
.transition-light-to-dark[aria-hidden="true"],
.transition-dark-to-light[aria-hidden="true"] {
  speak: none;
}

/* =============================================
   UTILITY CLASSES
   ============================================= */

/* Szybkie klasy dla różnych typów przejść */
.transition-smooth { transition: all 0.6s cubic-bezier(0.25, 0.1, 0.25, 1); }
.transition-organic { transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1); }
.transition-elegant { transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94); }

/* Opóźnienia dla staggered animacji */
.transition-delay-100 { transition-delay: 100ms; }
.transition-delay-200 { transition-delay: 200ms; }
.transition-delay-300 { transition-delay: 300ms; }
.transition-delay-400 { transition-delay: 400ms; }
.transition-delay-500 { transition-delay: 500ms; }

/* =============================================
   LAYOUT INTEGRATION FIXES
   ============================================= */

/* Ensure transitions don't interfere with section layout */
.rhythm-section.relative {
  position: relative;
  z-index: 1;
}

.rhythm-section.relative .transition-dark-to-light,
.rhythm-section.relative .transition-light-to-dark {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
  pointer-events: none;
}

/* Fix for bg-whisper sections with transitions */
.bg-whisper .transition-dark-to-light {
  /* Ensure proper blending with whisper background */
  background: linear-gradient(
    180deg,
    var(--charcoal) 0%,
    color-mix(in srgb, var(--charcoal) 85%, var(--whisper) 15%) 25%,
    color-mix(in srgb, var(--whisper) 80%, var(--sanctuary) 20%) 60%,
    color-mix(in srgb, var(--whisper) 95%, var(--charcoal) 5%) 85%,
    var(--whisper) 100%
  );
}

/* Fallback for bg-whisper sections */
.bg-whisper .transition-dark-to-light {
  background: linear-gradient(
    180deg,
    var(--charcoal) 0%,
    rgba(42, 39, 36, 0.9) 25%,
    rgba(249, 247, 243, 0.8) 60%,
    rgba(249, 247, 243, 0.95) 85%,
    var(--whisper) 100%
  );
}